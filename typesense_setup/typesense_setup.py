import typesense, os
import mysql.connector

client = typesense.Client({
  'nodes': [{
    'host': os.getenv("TS_HOST"),
    'port': '8108',
    'protocol': "http"
  }],
  'api_key': os.getenv("TS_API_KEY_ADMIN"),
  'connection_timeout_seconds': 2
})


"""
For spring up a local typesense server
docker run -p 8108:8108 -v ./typesense-data:/data typesense/typesense:0.25.2 --data-dir /data --api-key=admin123 --cors-domains http://localhost:5000

make sure api-key is for admin and match with .env
"""

def execute_sql(sql_script):
  connection = mysql.connector.connect(user=os.getenv("USER"), 
                                       password=os.getenv("PASSWORD"),
                                       host=os.getenv("HOST"),
                                       database=os.getenv("DATABASE"))
  cursor = connection.cursor()
  cursor.execute(sql_script)
  rows = cursor.fetchall()

  print(f'sql rows found: {len(rows)}')

  cursor.close()
  connection.close()
  return rows


def create_collection(collection_name, fields, documents):
  try:
    # Try to retrieve the collection
    client.collections[collection_name].retrieve()
    # If the collection exists, delete it
    client.collections[collection_name].delete()
  except typesense.exceptions.ObjectNotFound:
    # If the collection does not exist, do nothing
    pass

  schema = {
    'name': collection_name,
    'fields': fields
  }
  client.collections.create(schema)
  print(f'docs count: {len(documents)}')
  client.collections[collection_name].documents.import_(documents)

def create_documents(rows, field_names):
  documents = []
  for row in rows:
    document = {field_name: value for field_name, value in zip(field_names, row)}
    documents.append(document)
  return documents

def create_p_name_collection(limit:str=None):
  print(f'begin create p_name collection')
  limit = f'LIMIT {limit}' if limit else ''
  pname_script = f'SELECT DISTINCT name FROM Furniture {limit};'
  rows = execute_sql(pname_script)
  documents = create_documents(rows, ['name'])
  create_collection('p_name', [{'name': 'name', 'type': 'string'}], documents)

def create_p_type_collection(limit:str=None):
  print(f'begin create p_type collection')
  limit = f'LIMIT {limit}' if limit else ''
  sql_script = f"SELECT name, product_type FROM Furniture {limit};"
  rows = execute_sql(sql_script)
  name_type_set = set(f'{row[0]} {row[1]}' for row in rows)
  print(f'total name_type_set: {len(name_type_set)}')
  documents = create_documents([(name_type,) for name_type in name_type_set], ['name'])
  create_collection('p_type', [{'name': 'name', 'type': 'string'}], documents)

def create_parts_collection(limit:str=None):
  print(f'begin create parts collection')
  limit = f'LIMIT {limit}' if limit else ''
  sql_script = f"SELECT part_number FROM Part {limit};"
  rows = execute_sql(sql_script)
  documents = create_documents(rows, ['name'])
  create_collection('parts', [{'name': 'name', 'type': 'string'}], documents)
  
def create_anums_collection(limit:str=None):
  print(f'begin create anums collection')
  limit = f'LIMIT {limit}' if limit else ''
  sql_script = f"SELECT product_num FROM Furniture {limit};"
  rows = execute_sql(sql_script)
  
  modified_rows = []
  for row in rows:
    modified_product_num = '.'.join([row[0][i:i+3] for i in range(0, len(row[0]), 3)])
    modified_rows.append((modified_product_num,))

  documents = create_documents(modified_rows, ['name'])
  create_collection('anums', [{'name': 'name', 'type': 'string'}], documents)

def setup_collections():
  create_p_name_collection()
  create_p_type_collection()
  create_parts_collection()
  create_anums_collection()

def create_readonly_api_key():
  description = "Readonly API key for search"
  api_key_value = os.getenv("TS_API_KEY_RO")

  # Try to delete the existing API key
  try:
    existing_keys = client.keys.retrieve()
    for key in existing_keys['keys']:
      if key['description'] == description:
        client.keys[key['id']].delete()
        print(f'Deleted existing API key with id: {key["id"]}')
  except Exception as e:
    print(f'Error deleting existing API key: {e}')

  # Create a new API key
  try:
    key = client.keys.create({
      "description": description,
      "actions": ["documents:search"],
      "collections": ["*"],
      "value": api_key_value
    })
    print(f'Created new API key: {key}')
  except Exception as e:
    print(f'Error creating new API key: {e}')


if __name__ == '__main__':
  print(f'TS_HOST: {os.getenv("TS_HOST")} -- should be typesense-sv on production')
  print(f'TS_API_KEY_ADMIN: {os.getenv("TS_API_KEY_ADMIN")}')

  setup_collections()
  create_readonly_api_key()
  print('done setting up typesense collections')