# Use an official Python runtime as a parent image
FROM python:3.11.6

# When this is set to 1, Python won't buffer the outputs of your program. 
# This is useful in a Docker container because it allows the logs from your 
# Python application to be displayed in real-time. Without this, 
# Python might buffer the output, meaning you wouldn't see the logs immediately.
ENV PYTHONUNBUFFERED 1

# mkdir /code to create a directory in the container
RUN mkdir /code

# Set the working directory in the container to /code
WORKDIR /code

# Copy only requirements.txt first to leverage Docker cache
COPY ./requirements.txt /code/

# Install any needed packages specified in requirements.txt
RUN pip install -r requirements.txt

# Copy the current directory contents (e.g my local codebase) into the container at /code
COPY . /code/

CMD [ "python", "./typesense_setup.py" ]