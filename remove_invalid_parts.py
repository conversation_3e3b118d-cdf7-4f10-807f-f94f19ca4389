#!/usr/bin/env python3
"""
Script to remove invalid parts from the MySQL database.

This script:
1. Gets all part_number and id from the Part table
2. Validates each part_number using the IKEA API
3. Removes invalid parts (those that return empty/null data) from the database

Usage:
    python remove_invalid_parts.py [--dry-run] [--batch-size N] [--delay N]
    
Options:
    --dry-run: Show what would be deleted without actually deleting
    --batch-size: Number of parts to process in each batch (default: 100)
    --delay: Delay in seconds between API calls (default: 0.1)
"""

import os
import sys
import time
import argparse
import requests
import mysql.connector
from dotenv import load_dotenv
from contextlib import contextmanager
from typing import List, Tuple, Optional, Dict, Any

# Load environment variables
load_dotenv()

@contextmanager
def create_connection():
    """Create a MySQL database connection using environment variables."""
    conn = mysql.connector.connect(
        user=os.getenv("USER"),
        database=os.getenv("DATABASE"),
        host=os.getenv("C_HOST"),
        port=os.getenv("C_PORT"),
        password=os.getenv("C_PASSWORD")
    )
    try:
        yield conn
    finally:
        conn.close()

def fetch_part_json(part_number: str) -> Optional[Dict[Any, Any]]:
    """
    Fetch part data from IKEA API to validate if part exists.
    
    Args:
        part_number: The part number to validate
        
    Returns:
        Dict with part data if valid, None if invalid
    """
    url = f'https://api.prod.apo.ingka.com/part/{part_number}'
    headers = {
        'accept': 'application/json',
        'accept-language': 'en-us',
        'content-language': 'en-us',
        'content-type': 'application/json',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        # Check if the response contains valid part data
        # Invalid parts return None or don't have itemId
        if data is None or data.get('itemId') is None:
            return None
        
        return data
    except Exception as e:
        print(f"Error fetching part {part_number}: {e}")
        return None

def get_all_parts() -> List[Tuple[int, str]]:
    """
    Get all part_number and id from the Part table.
    
    Returns:
        List of tuples (id, part_number)
    """
    with create_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT id, part_number FROM Part ORDER BY id")
        parts = cursor.fetchall()
        cursor.close()
        return parts

def delete_part_by_id(part_id: int, part_number: str, dry_run: bool = False) -> bool:
    """
    Delete a part and its associations from the database.
    
    Args:
        part_id: The ID of the part to delete
        part_number: The part number (for logging)
        dry_run: If True, don't actually delete, just log what would be deleted
        
    Returns:
        True if deletion was successful (or would be successful in dry run)
    """
    if dry_run:
        print(f"[DRY RUN] Would delete part ID {part_id} (part_number: {part_number})")
        return True
    
    try:
        with create_connection() as conn:
            cursor = conn.cursor()
            
            # Start transaction
            conn.start_transaction()
            
            # First delete from PdfPart table (foreign key constraint)
            cursor.execute("DELETE FROM PdfPart WHERE part_id = %s", (part_id,))
            pdf_part_deleted = cursor.rowcount
            
            # Then delete from Part table
            cursor.execute("DELETE FROM Part WHERE id = %s", (part_id,))
            part_deleted = cursor.rowcount
            
            # Commit transaction
            conn.commit()
            cursor.close()
            
            print(f"Deleted part ID {part_id} (part_number: {part_number}) - "
                  f"PdfPart records: {pdf_part_deleted}, Part records: {part_deleted}")
            return True
            
    except Exception as e:
        print(f"Error deleting part ID {part_id} (part_number: {part_number}): {e}")
        try:
            conn.rollback()
        except:
            pass
        return False

def main():
    parser = argparse.ArgumentParser(description='Remove invalid parts from MySQL database')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be deleted without actually deleting')
    parser.add_argument('--batch-size', type=int, default=100,
                       help='Number of parts to process in each batch (default: 100)')
    parser.add_argument('--delay', type=float, default=0.1,
                       help='Delay in seconds between API calls (default: 0.1)')
    
    args = parser.parse_args()
    
    print("Starting invalid parts removal process...")
    print(f"Mode: {'DRY RUN' if args.dry_run else 'LIVE'}")
    print(f"Batch size: {args.batch_size}")
    print(f"API delay: {args.delay} seconds")
    print("-" * 50)
    
    # Get all parts from database
    print("Fetching all parts from database...")
    all_parts = get_all_parts()
    total_parts = len(all_parts)
    print(f"Found {total_parts} parts in database")
    
    if total_parts == 0:
        print("No parts found in database. Exiting.")
        return
    
    # Process parts in batches
    invalid_parts = []
    processed = 0
    
    for i in range(0, total_parts, args.batch_size):
        batch = all_parts[i:i + args.batch_size]
        batch_num = (i // args.batch_size) + 1
        total_batches = (total_parts + args.batch_size - 1) // args.batch_size
        
        print(f"\nProcessing batch {batch_num}/{total_batches} ({len(batch)} parts)...")
        
        for part_id, part_number in batch:
            processed += 1
            
            # Validate part via API
            part_data = fetch_part_json(part_number)
            
            if part_data is None:
                print(f"  [{processed}/{total_parts}] INVALID: {part_number} (ID: {part_id})")
                invalid_parts.append((part_id, part_number))
            else:
                print(f"  [{processed}/{total_parts}] VALID: {part_number} (ID: {part_id})")
            
            # Add delay between API calls to be respectful
            if args.delay > 0:
                time.sleep(args.delay)
    
    print(f"\n" + "=" * 50)
    print(f"SUMMARY:")
    print(f"Total parts processed: {processed}")
    print(f"Valid parts: {processed - len(invalid_parts)}")
    print(f"Invalid parts found: {len(invalid_parts)}")
    
    if len(invalid_parts) == 0:
        print("No invalid parts found. Nothing to delete.")
        return
    
    print(f"\nInvalid parts to be {'removed' if not args.dry_run else 'marked for removal'}:")
    for part_id, part_number in invalid_parts:
        print(f"  - ID: {part_id}, Part Number: {part_number}")
    
    if not args.dry_run:
        confirm = input(f"\nAre you sure you want to delete {len(invalid_parts)} invalid parts? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Deletion cancelled.")
            return
    
    # Delete invalid parts
    print(f"\n{'[DRY RUN] Simulating deletion of' if args.dry_run else 'Deleting'} invalid parts...")
    deleted_count = 0
    
    for part_id, part_number in invalid_parts:
        if delete_part_by_id(part_id, part_number, args.dry_run):
            deleted_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"FINAL SUMMARY:")
    print(f"Parts {'marked for deletion' if args.dry_run else 'successfully deleted'}: {deleted_count}")
    print(f"Parts failed to delete: {len(invalid_parts) - deleted_count}")
    
    if args.dry_run:
        print("\nThis was a dry run. No actual changes were made to the database.")
        print("Run without --dry-run to perform actual deletions.")

if __name__ == "__main__":
    main()
