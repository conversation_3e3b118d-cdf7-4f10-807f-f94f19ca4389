**To debug on local machine**
- Kill all lingering instant of Flask server by running this cmd: taskkill /f /im python.exe
- Uncomment dev_config in .env/ ## CRAWLER CONFIG ##
- Make sure dev MyS<PERSON> has matching host (ie localhost:3306) and MySQL Workbench is running
- Make sure correct Schema exists (if no Schema, create one with matching name -> use table_creation script if no tables)
- No need to touch FLASK CONFIG
- Login to Bunny Storage to see uploaded files (consider pointing to a different bucket if testing)

**IN CASE I FORGOT**
- run cmd on windows > ssh to cloud `ssh root@***********`
- navigate to project folder > `cd ..` > `cd var/app/manual_db`
- `git pull` to pull the code
- `docker-compose down` to turn off the current app instance
- `docker-compose up --build` to apply new codes and rebuild


Testing:
- Kill a running python processes to prevent leftover instance of flask server. 
    run cmd to kill python process and restart `taskkill /IM python.exe /F`
- test should run on dev environment without the need to change .env file


**Checklist for DEPLOY**
0) Run test, check docker-compose.yml to ensure new ENV is added to the webapp service
1) Check .env for: 
    a. C_HOST, C_PORT and C_PASSWORD pointing to production sql
    b. Flask config section should NOT not be touched
    c. DATABASE has correct schema name
    d. BUCKETNAME (Bunny Storage Zone for file upload) has correct name
    e. if encounter error uploading, check printed API keys in console

2) If using cloudflare, make sure SSL/TSL rule is set to Full (Strict)
3) app.run(host=0.0.0.0, port=5000) 
4) Dockerfile for webapp should use Gunicorn command
5) Make sure product_parser has the TODO or XXX section uncommented 
6) Copy env file from local machine to remote via cmd
`scp ./.env root@***********:/var/app/manual_db_site`


**Troubleshoot MYSQL DB**
- To perform Full Text search on Furniture Type, There are 3 conditions:
1) the `my.ini` has to have this line `innodb_ft_min_token_size=1` under [mysqld] and then restart the SQL server.
- location of `my.ini` is `C:\ProgramData\MySQL\MySQL Server 8.0`

# To restart MySQL server on windows
- Win + R, typing services.msc look for MySQL service name (ie MySQL26cd)
- net stop <mysqlname>
- net start <mysqlname>
- after restart, run this `SHOW VARIABLES LIKE 'innodb_ft_min_token_size';` to check if the value is 2

2) `name_w_type` column need to be FULLTEXT indexed (this is done during table creation). If not run this `ALTER TABLE Furniture ADD FULLTEXT(name_w_type);` to index it manually.

3) run `OPTIMIZE TABLE your_table_name;` to rebuild the FULLTEXT index


**IMPORTANT NOTES:**
1) table names are case sensitive in linux
2) watch out for occupied port, MySQL often runs on 3306 so if there is another instance of MySQL running on your machine, it will not work. Change the Dockerized SQL server port to 3307 (left side of the <port>:<port>)
3) .env file has to be same dir level as docker-compose.yml for it to read
4) .env file has no space around "=" and no need to wrap value in quotes
5) If app.config['SQLALCHEMY_DATABASE_URI'] did not load the correct value in .env, it may have been cached. Restart IDE will fix
6) Modify Dockerfile in webapp to switch to DEBUG mode
7) If nginx error 1.19.92, make sure app.run() has port and host specified


**DEPLOY TO UBUNTU**
**Login to cloud server + install docker-compose**
to SSH from windows 10 to cloud (to access from different device, copy private key from previous device to id_rsa on new device)
`ssh root@***********`


if warning @@@ man in middle @@@ show up, do this to remove the old ip from cloud from hostfile
`ssh-keygen -R ************`
then retry SSH, it should prompt for yes/no this time

If using hetzner cloud server. They have docker installed but docker-compose is not setup in the path. This is the cmd to set docker-compose up
`sudo curl -L https://github.com/docker/compose/releases/download/1.21.0/docker-compose-$(uname -s)-$(uname -m) -o /usr/local/bin/docker-compose`
`sudo chmod +x /usr/local/bin/docker-compose`

**Download codebase to server**
git clone a private project via ssh will need a public key create one by using this 
`cd ~/.ssh && ssh-keygen # hit enter on everything to create default key`
`cat id_rsa.pub` then copy content in this file to github to create a public key

Copy env file from local machine to remote via cmd
`scp ./.env root@***********:/var/app/manual_db_site`
`scp ./.env root@***********:/var/app/manual_db_site` for int-sv

**Install app**
navigate to project folder where the docker-compose.yml is
`docker-compose up --build` to deploy

**Initial MySQL Setup**
First time MySQL setup, it will not have any tables so we can either run this in cmd: 
`docker exec -it docker2-db-1 mysql -u root -pjim12345 new_schema1`
to open interactive shell and execute the table_creation script. Then run SHOW TABLES; to make sure tables are created succesfully

-OR-

Download MySQL Workbench on the laptop and connect to the dockerized MySQL via the port **3307** (if 3306 is occupied)
Then execute the table_creation script on there

**Checking site works**
- If site fail to load, use firefox or clearing browsing data and cookies on Chrome
- If seeing undesired behaviors such as changes did not take effect, clear browser history on phone, desktop.


**BACKUP & RESTORE**
back and restore MySQL through Workbench by using export and import features

**REFERENCE VIDEOS**
https://youtu.be/p0HnnJ6CusM?si=3EdykCSrjyFGUq2V
https://www.youtube.com/watch?v=vTVnAm8VNbM
https://youtu.be/oIm5sBs3K6M?si=TXKh0sI6I6_5dW1h

https://www.youtube.com/watch?v=5meiTtVRtRc

caddy: 
https://www.youtube.com/watch?v=6TM9ds1fsug
https://www.youtube.com/watch?v=vfZgHX5ttsY

To discard changes and update git again
`git reset --hard`
`git pull`