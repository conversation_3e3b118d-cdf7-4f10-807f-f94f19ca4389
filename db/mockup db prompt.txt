Prompts: 
give me an example of how the table will look like if I have 
2 pdf files named: EKTORP.pdf, LACK.pdf that contain part number: 122332

give me an example of how the table will look like if I have a furniture called LACK that has 
2 pdf files named: LACK-1.pdf, LACK-2.pdf that contain part number: 122332

SELECT f.name AS furniture_name, pf.file_name, p.part_number
FROM Furniture f
JOIN PdfFile pf ON f.id = pf.furniture_id
JOIN Part p ON pf.id = p.pdf_file_id
WHERE f.name = 'LACK';

SELECT PdfFile.*
FROM PdfFile
JOIN Part ON PdfFile.id = Part.pdf_file_id
WHERE Part.part_number = '122332';PdfFile Table:

| id | file_name   | file_path       |
|----|-------------|-----------------|
| 1  | EKTORP.pdf  | /path/to/EKTORP |
| 2  | LACK.pdf    | /path/to/LACK   |

Part Table:

| id | part_number | description | pdf_file_id |
|----|-------------|-------------|-------------|
| 1  | 122332      | ...         | 1           |
| 2  | 122332      | ...         | 2           |

