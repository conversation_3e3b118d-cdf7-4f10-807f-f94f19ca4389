
### make sure to update test script if you change this file or the test will fail

CREATE TABLE Furniture (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255),
    product_num VARCHAR(255),
    product_type VARCHAR(255),
    name_w_type TEXT,
    details TEXT,
    measurements TEXT,
    image_dir VARCHAR(255),
    FULLTEXT(name_w_type)
);
        
        
CREATE TABLE PdfFile (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) UNIQUE,
    file_path VARCHAR(255),
    page_count INT
);

CREATE TABLE FurniturePdf (
    furniture_id INT,
    pdf_file_id INT,
    PRIMARY KEY (furniture_id, pdf_file_id),
    FOREIGN KEY (furniture_id) REFERENCES Furniture(id),
    FOREIGN KEY (pdf_file_id) REFERENCES PdfFile(id)
);

CREATE TABLE Part (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_number VARCHAR(255) UNIQUE,
    partName TEXT,
    partDescription TEXT,
    partImage VARCHAR(255)
);
        
CREATE TABLE PdfPart (
    pdf_file_id INT,
    part_id INT,
    PRIMARY KEY (pdf_file_id, part_id),
    FOREIGN KEY (pdf_file_id) REFERENCES PdfFile(id),
    FOREIGN KEY (part_id) REFERENCES Part(id)
);

CREATE TABLE crawled_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_url VARCHAR(255) NOT NULL
);