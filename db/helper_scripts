### use this script to insert part to a specific pdf file
INSERT IGNORE INTO Part (part_number) VALUES ('117687');
SET @part_id = (SELECT id FROM Part WHERE part_number = '104663');
SET @pdf_file_id = (SELECT id FROM PdfFile WHERE file_name = 'komplement-soft-closing-hinge__AA-1344860-2-2.pdf');
INSERT INTO PdfPart (pdf_file_id, part_id) VALUES (@pdf_file_id, @part_id);


### use this script to find the model name based on pdf file name
SELECT FurnitureFurniture.product_num
FROM Furniture
INNER JOIN FurniturePdf ON Furniture.id = FurniturePdf.furniture_id
INNER JOIN PdfFile ON FurniturePdf.pdf_file_id = PdfFile.id
WHERE PdfFile.file_name = 'hjaelpa-soft-closing-hinge__AA-2210023-4.pdf';


### use this script to delete part from a specific pdf file
DELETE PdfPart FROM PdfPart
JOIN Part ON PdfPart.part_id = Part.id
JOIN PdfFile ON PdfPart.pdf_file_id = PdfFile.id
WHERE Part.part_number = '100712' AND PdfFile.file_name LIKE '%AA-2053714-1-2%';


### use this script to delete part from all associated pdf files
START TRANSACTION;
DELETE FROM PdfPart 
WHERE part_id = (SELECT id FROM Part WHERE part_number = '113344');
DELETE FROM Part 
WHERE part_number = '113344';
COMMIT;