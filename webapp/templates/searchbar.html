<div class="col-12 col-md-8 mx-auto text-center mb-3">
  <h1 id="pageTitle" class="display-4 mt-3"><strong>EASY REBUILD</strong></h1>
  <h2 id="pageSubtitle" class="lead">Search IKEA manuals and parts</h2>
</div>

<script src="{{ url_for('static', filename='typesense.min.js') }}"></script>

<div class="search-container mb-3">
  <div class="container">
    <form action="/search" id="searchForm" method="GET" class="col-12 col-md-8 mx-auto"
      onsubmit="return validateSearchTerm()">
      <div class="form-group position-relative">
        <div class="input-group input-group mb-3">
          <input id="searchInput" type="search" name="q" class="form-control rounded-pill" value="{{ q }}"
            placeholder="Enter your search term" aria-label="Search term" required>
          <button id="searchButton" type="submit" class="btn btn-primary rounded-pill ms-2" aria-label="Submit search">
            <i class="bi bi-search"></i>
          </button>
        </div>
        <div id="searchAutocomplete" class="dropdown-menu w-100 position-absolute"></div>
      </div>

      <div class="form-group">
        <label class="d-block mb-2 fw-bold">Search By:</label>
        <div class="btn-group btn-group-toggle w-100 mb-3" role="group" aria-label="Search type">
          <input type="radio" class="btn-check" name="searchType" id="typeRadio" value="type" autocomplete="off" {% if not request.args.get('searchType') or request.args.get('searchType')=='type' %}checked{% endif %}>
          <label class="btn btn-outline-primary" for="typeRadio">Product Type</label>

          <input type="radio" class="btn-check" name="searchType" id="anumRadio" value="anum" autocomplete="off" {% if request.args.get('searchType')=='anum' %}checked{% endif %}>
          <label class="btn btn-outline-primary" for="anumRadio">Article Number</label>

          <input type="radio" class="btn-check" name="searchType" id="partRadio" value="part" autocomplete="off" {% if request.args.get('searchType')=='part' %}checked{% endif %}>
          <label class="btn btn-outline-primary" for="partRadio">Part Number</label>
        </div>
      </div>
    </form>
  </div>
</div>

<script>
  var client = new Typesense.Client({
    nodes: [
      {
        host: "{{ ts_host }}",
        protocol: "{{ ts_protocol }}",
        {% if ts_port %}port: "{{ ts_port }}", {% endif %}
      },
    ],
    apiKey: "{{ ts_api_key }}",
    connectionTimeoutSeconds: 2,
  });
</script>
<script src="{{ url_for('static', filename='searchbar.js') }}"></script>

<div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="errorModalLabel">Error</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="errorModalBody">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>