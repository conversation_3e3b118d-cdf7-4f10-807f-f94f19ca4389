{% extends "base.html" %}
{% block canonical_link %}
{% set url_parts = request.url.split('?') %}
<link rel="canonical" href="{{ url_parts[0] }}" />
{% endblock %}

{% block content %}
<main class="pb-5">
    <div class="container">
        <!-- mobile bottom nav -->
        <nav class="mobile-bottom-nav d-md-none">
            <div class="mobile-bottom-nav__item m-2">
                <a href="javascript:history.back()" class="mobile-bottom-nav__item-content"><i
                        class="bi bi-arrow-left-square"></i> Back</a>
            </div>
            <div class="mobile-bottom-nav__item">
                <a href="#tableOfContents" class="mobile-bottom-nav__item-content"><i class="bi bi-card-list"></i>Table
                    of Contents</a>
            </div>
        </nav>
        <!-- end mobile bottom nav -->

        <!-- summary -->
        <div class="row mt-5">
            <div class="col-12">
                <h1 class="mb-3">IKEA {{ name_type }} instructions</h1>
                <p>
                    <strong>Full Product Name:</strong> IKEA {{ product.name_w_type }}<br>
                    <strong>Article Number:</strong> {{ product.product_num }}<br>
                    <strong>Number of user manuals:</strong> {{product.pdfs|length}}
                </p>
            </div>
        </div>
        <!-- end summary -->

        <div class="row mt-4">
            <!-- toc -->
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm" id="tableOfContents">
                    <div class="card-body">
                        <h3 class="card-title">Table of Contents</h3>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="#manualContainer" class="text-decoration-none fw-bold fs-6">&#10095; Manuals
                                    and Parts List</a>
                                <ul class="list-unstyled ms-3 mt-1">
                                    {% for pdf in product.pdfs %}
                                    <li><a href="#manual{{ loop.index }}" class="text-decoration-none">&#9658; IKEA {{
                                            pdf.file_name }}</a></li>
                                    {% endfor %}
                                </ul>
                            </li>
                            <li class="mb-2"><a href="#productDetailContainer"
                                    class="text-decoration-none fw-bold fs-6">&#10095; Product Detail Specification</a>
                            </li>
                            <li class="mb-2"><a href="#relatedProductsContainer"
                                    class="text-decoration-none fw-bold fs-6">&#10095; Related Products</a></li>
                            <li class="mb-2"><a href="#askQuestionContainer"
                                    class="text-decoration-none fw-bold fs-6">&#10095; Question and Answer</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- end toc -->

            <!-- content viewer -->
            <div class="col-md-9">
                <div id="manualContainer">
                    {% set pdf_count = product.pdfs|length %}
                    <h2 class="mb-4">Manuals and Parts List</h2>
                    <p class="lead">
                        {% if pdf_count == 1 %}
                        There is <strong>one</strong> assembly instruction manual to fully build <em>IKEA {{ name_type
                            }}.</em>
                        {% elif pdf_count > 1 %}
                        There are <strong>{{ pdf_count }}</strong> assembly instruction manuals to fully build <em>IKEA
                            {{ name_type }}.</em>
                        Scroll down to view all.
                        {% endif %}
                    </p>

                    {% for pdf in product.pdfs %}
                    {% set image_url = pdf.pdf_path %}
                    {% set page_count = pdf.page_count %}
                    {% set parts = pdf.parts %}
                    {% set file_name = pdf.file_name %}
                    <div id="manual{{ loop.index }}" class="mb-5">
                        <h4 class="mb-3">{{loop.index}}. Instructions for IKEA {{ file_name }}</h4>
                        <div class="card mb-4 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title">IKEA parts list for <em>{{ file_name }}</em>:</h5>
                                <div class="d-flex flex-wrap">
                                    {% if parts %}
                                    {% for part in parts %}
                                    <a href="{{ url_for('search', q=part, searchType='part') }}">
                                        <span
                                            class="part-badge badge bg-{{ 'warning' if part == q else 'primary' }} text-decoration-none m-1 fs-6">{{
                                            part }}</span>
                                    </a>
                                    {% endfor %}
                                    {% else %}
                                    <p class="text-muted">No hardware or fittings found for <em>{{ file_name }}</em>.
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card shadow-sm">
                            <div id="viewerContainer{{ loop.index0 }}" data-page-count="{{ page_count }}"
                                class="viewer-container">
                                {% for i in range(1, page_count + 1) %}
                                {% if i % 2 != 0 %}
                                <div class="page-pair">
                                    {% endif %}
                                    <img id="imgViewer{{ loop.index0 }}_{{ i }}"
                                        src="{{ image_url|replace('p1.png', 'p' ~ i ~ '.png') }}"
                                        alt="Assembly instructions for IKEA {{ file_name }} | Page {{ i }} - IKEA {{ name_type }} {{ product.product_num }}"
                                        class="img-fluid" loading="lazy" />
                                    {% if i % 2 == 0 or i == page_count %}
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- product detail -->
                <div id="productDetailContainer" class="mb-5">
                    <h2 class="mb-4">IKEA {{ product.name }} Product Details</h2>
                    <div class="row">
                        <div class="col-md-5">
                            <img id="mainImg" src="{{ product.image_dir }}" alt="IKEA {{ name_type }} Model Image"
                                class="img-fluid mb-3" loading="lazy" />
                        </div>
                        <div class="col-md-7">
                            <h3>{{ product.name_w_type }}</h3>
                            <h5>Article number: <span class="badge bg-secondary">{{ product.product_num }}</span></h5>
                            {% for detail_paragraph in product.details %}
                            <p>{{ detail_paragraph }}</p>
                            {% endfor %}
                            {% if product.measurements %}
                            <h5 class="mt-4">Measurements:</h5>
                            {% for measurement in product.measurements %}
                            <span>
                                <strong>{{ measurement.typeName }}:</strong>
                                <span>{{ measurement.textImperial }} </span>
                                <span>({{ measurement.textMetric }})</span>
                                <br />
                            </span>
                            {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
                <!-- end product detail -->

                <!-- related products -->
                <div id="relatedProductsContainer" class="mb-5">
                    <h2 class="mb-4">Related Product Manuals</h2>
                    {% if search_results %}
                    <div class="row row-cols-1 row-cols-md-2 g-4">
                        {% for result in search_results %}
                        <div class="col">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body">
                                    <a href="{{ url_for('details', id=result.id, stag=result.stag, q=result.name) }}"
                                        class="text-decoration-none">
                                        <img src="{{ result.thumbnail }}" alt="IKEA {{ result.name }} thumbnail image"
                                            class="img-thumbnail float-start me-3" style="width: 68px; height: 68px;"
                                            loading="lazy">
                                        <h5 class="card-title">IKEA {{ result.name }} Manual</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="mt-4">
                        <a href="{{ url_for('model', model_name=product.name) }}" class="btn btn-outline-primary mb-2">
                            View all IKEA {{ product.name }} model manuals</a>
                        <a href="{{ url_for('search', q=product.product_type, searchType='type') }}"
                            class="btn btn-outline-primary mb-2"> View all IKEA {{ product.product_type }} manuals</a>
                        <a href="{{ url_for('search', q='100001', searchType='part') }}"
                            class="btn btn-outline-primary mb-2"> Search any IKEA hardware parts</a>
                        <a href="{{ url_for('search', q=product.product_num, searchType='anum') }}"
                            class="btn btn-outline-primary mb-2"> Search by IKEA article number</a>
                    </div>
                </div>
                <!-- end related products -->

                <!-- ask question -->
                <div id="askQuestionContainer" class="mb-5">
                    <h2 class="mb-4">Need help?</h2>
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">Have a question about IKEA {{ product.name }} or Need to request a
                                missing manual?</h5>
                            <form id="askQuestionForm">
                                <div class="form-group" data-requestUrl="{{ request.url }}" data-fid="{{ id }}">
                                    <textarea name="message" class="form-control mb-3" rows="2" required minlength="12"
                                        placeholder="Type your question here..." data-bs-toggle="modal"
                                        data-bs-target="#questionModal" readonly></textarea>
                                    <div class="text-end">
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                            data-bs-target="#questionModal">Ask a question</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- end ask question -->

                <!-- questions and answers -->
                <div id="questionAnswerContainer">
                    <h2 class="mb-4">Questions and Answers</h2>
                    <h5 class="text-muted mb-4">Number of questions: {{ questions|length }}</h5>
                    {% for question in questions %}
                    <div class="card mb-4 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary text-white rounded-circle p-2 me-3">
                                    {{ question.user[0]|upper }}
                                </div>
                                <div>
                                    <h5 class="card-title mb-0">{{ question.user }}</h5>
                                    <small class="text-muted">Posted on {{ question.posted_at }}</small>
                                </div>
                            </div>
                            <p class="card-text">{{ question.text|replace('\n', '<br />')|safe }}</p>
                            {% for answer in question.answers %}
                            <div class="card ms-5 mb-3 bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="bg-success text-white rounded-circle p-2 me-3">
                                            {{ answer.user[0]|upper }}
                                        </div>
                                        <div>
                                            <h6 class="card-title mb-0">{{ answer.user }}</h6>
                                            <small class="text-muted">Answered on {{ answer.posted_at }}</small>
                                        </div>
                                    </div>
                                    <p class="card-text">{{ answer.text|replace('\n', '<br />')|safe }}</p>
                                </div>
                            </div>
                            {% endfor %}
                            <div class="mt-3">
                                <button class="btn btn-outline-primary" data-bs-toggle="modal"
                                    data-bs-target="#commentModal" data-qid="{{ question.question_id }}"
                                    data-asker="{{ question.user }}" data-question="{{ question.text }}"
                                    data-qtime="{{ question.posted_at }}">
                                    Add a comment
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <!-- end questions and answers -->
            </div>
            <!-- end content viewer -->
        </div>
    </div>
</main>

<!-- question modal -->
<div class="modal fade" id="questionModal" tabindex="-1" aria-labelledby="questionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="questionModalLabel">Ask a question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="questionForm" novalidate>
                    <div class="mb-3">
                        <textarea class="form-control" id="qnaQuestion" rows="3"
                            placeholder="Type your question here..." required minlength="12"></textarea>
                        <div class="invalid-feedback">
                            Please enter your question (minimum 12 characters).
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="qnaQuestionName" placeholder="Your name" required>
                        <div class="invalid-feedback">
                            Please enter your name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="email" class="form-control" id="qnaQuestionEmail" placeholder="Your email"
                            required>
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button id="submitQuestionBtn" type="button" class="btn btn-primary">Submit</button>
            </div>
            <div class="p-3 modal-result"></div>
        </div>
    </div>
</div>
<!-- end question modal -->

<!-- answer modal -->
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentModalLabel">Response to the question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-2">
                            <div id="userAvatarComment" class="bg-primary text-white rounded-circle p-2 me-3"></div>
                            <div>
                                <h6 class="card-title mb-0" id="asker"></h6>
                                <small class="text-muted" id="qtime"></small>
                            </div>
                        </div>
                        <p class="card-text" id="questionText"></p>
                    </div>
                </div>
                <form id="commentForm" novalidate>
                    <div class="mb-3">
                        <textarea class="form-control" id="commentText" rows="3"
                            placeholder="Type your comment to the question above ..." required></textarea>
                        <div class="invalid-feedback">
                            Please enter your comment.
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="commentName" placeholder="Your name" required>
                        <div class="invalid-feedback">
                            Please enter your name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="email" class="form-control" id="commentEmail" placeholder="Your email" required>
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                    <input type="hidden" id="qid">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="submitCommentBtn">Submit</button>
            </div>
            <div class="p-3 modal-result"></div>
        </div>
    </div>
</div>
<!-- end answer modal -->

<script src="{{ url_for('static', filename='details.js') }}"></script>
{% endblock %}