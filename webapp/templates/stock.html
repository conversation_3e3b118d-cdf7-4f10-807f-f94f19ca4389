{% extends "base.html" %}

{% block canonical_link %}
<link rel="canonical" href="{{ url_for('stock', _external=True) }}">
{% endblock %}

{% block content %}
<main class="pb-5">
    <div class="container my-5">
        <div class="col-12 col-md-8 mx-auto text-center mb-3">
            <h1 id="pageTitle" class="display-4 mt-3"><strong>IKEA STOCK CHECKER</strong></h1>
            <h2 id="pageSubtitle" class="lead">Check IKEA product availability and restock schedule in any stores
                worldwide</h2>
        </div>

        <!-- searchbar -->
        <div class="search-container mb-3">
            <div class="container">
                <form action="{{ url_for('stock') }}" id="stockSearchForm" method="GET" class="col-8 col-md-6 mx-auto">
                    <div class="form-group position-relative">
                        <div class="row g-2 align-items-center">
                            <div class="col-md-4">
                                <select name="country" class="form-select rounded-pill" aria-label="Select country">
                                    {% for region in ['North America', 'South America', 'Europe', 'Asia', 'Africa',
                                    'Oceania'] %}
                                    <optgroup label="{{ region }}">
                                        {% for code, country in countries.items() %}
                                        {% if country.region == region %}
                                        <option value="{{ code }}" {% if code==qCountry %}selected{% endif %}>
                                            {{ country.name }}
                                        </option>
                                        {% endif %}
                                        {% endfor %}
                                    </optgroup>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-6">
                                <div class="input-group">
                                    <input id="stockSearchInput" type="text" name="itemNo"
                                        class="form-control rounded-pill" value="{{ qItemNo }}"
                                        placeholder="Enter IKEA product number" aria-label="Stock Check Search term">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button id="stockSearchButton" type="submit" class="btn btn-primary rounded-pill w-100"
                                    aria-label="Submit stock search">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div id="searchError" class="invalid-feedback mt-2" style="display: none;"></div>
                    </div>
                </form>
            </div>

            <!-- faq -->
            {% if not availabilities and not qItemNo %}
            <div class="row my-2">
                <div class="col-10 col-md-8 mx-auto my-3">
                    <!-- q1 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                How to use?
                            </button>
                        </h3>
                        <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="heading1">
                            <div class="accordion-body">
                                <ul>
                                    <li>Select your country</li>
                                    <li>Enter 8-digit product number</li>
                                    <li>View results showing in-store stock levels across all stores</li>
                                    <li>Click on individual stores to see restock schedule</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- q2 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading2">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                How accurate is the IKEA Stock Checker for in-store availability?
                            </button>
                        </h3>
                        <div id="collapse2" class="accordion-collapse collapse show" aria-labelledby="heading2">
                            <div class="accordion-body">
                                <p>Our stock checker provides near real-time inventory updates from stores
                                    worldwide. Stock levels are usually most accurate early in the morning after the
                                    overnight restock schedule is completed.</p>
                            </div>
                        </div>
                    </div>

                    <!-- q3 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading3">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                How often do IKEA restock sold out items?
                            </button>
                        </h3>
                        <div id="collapse3" class="accordion-collapse collapse show" aria-labelledby="heading3">
                            <div class="accordion-body">
                                <p>IKEA's restock schedule varies by location, with most stores receiving new inventory
                                    1-3 times per week. Our Stock Checker helps you find when items will be back in
                                    stock.</p>
                                <p>For out-of-stock items without a listed restock date, we recommend checking
                                    back later as schedules are updated regularly. Popular and high-demand items
                                    typically get restocked more frequently</p>
                            </div>
                        </div>
                    </div>

                    <!-- q4 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading4">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                Technical support?
                            </button>
                        </h3>
                        <div id="collapse4" class="accordion-collapse collapse show" aria-labelledby="heading4">
                            <div class="accordion-body">
                                <p>Send me a message to <a class="text-primary font-weight-bold" href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            {% endif %}
            <!-- end faq -->
        </div>
        <!-- end searchbar -->

        {% if qItemNo %}
        <div class="row justify-content-center mb-3">
            <div class="col-12 col-md-8 text-center">
                {% if availabilities %}
                <h3>Results for <strong>"{{ qItemNo }}"</strong> in {{ availabilities | length }} {{
                    country_name }}
                    stores</h3>

                {% if product_info %}
                <div class="row justify-content-center mb-2">
                    <div class="col-md-10 col-lg-8">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="row g-0 align-items-center p-3">
                                <div class="col-5">
                                    <img src="{{ product_info.get('image') }}" class="img-fluid rounded p-2"
                                        style="width: auto; height: 160px; object-fit: contain;"
                                        alt="IKEA part number {{ q }} image">
                                </div>
                                <div class="col-7">
                                    <div class="card-body">
                                        <p class="card-title mb-0">{{ product_info.get('title') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% else %}
                <h3 class="text-danger">"{{ qItemNo }}" is not a valid product number in IKEA {{ country_name }}
                </h3>
                {% endif %}
            </div>
        </div>

        <!-- table -->
        {% if availabilities %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover text-center">
                        <thead>
                            <tr>
                                <th>Store</th>
                                <th>Qty (in store)</th>
                                <th>Buying Options</th>
                                <th>Restock Schedule</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for store in availabilities %}
                            <tr class="align-middle expandable-row" data-bs-toggle="collapse"
                                data-bs-target="#details-{{ loop.index }}" style="cursor: pointer;">
                                <td>{{ store.name }}</td>
                                <td class="{% if store.quantity > 0 %}bg-success text-white{% endif %}">
                                    {{ store.quantity }}
                                </td>
                                <td class="text-success">
                                    {% if store.is_pickup %}
                                    <i class="bi bi-building" title="In-store Pickup"></i>
                                    {% endif %}
                                    {% if store.is_click_collect %}
                                    <i class="bi bi-bag" title="Click & Collect"></i>
                                    {% endif %}
                                    {% if store.is_home_delivery %}
                                    <i class="bi bi-truck" title="Home Delivery"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if store.earliestRestock %}
                                    <i class="bi bi-shield-fill-check text-success" title="Restock scheduled"></i>
                                    {% else %}
                                    <i class="bi bi-shield-exclamation text-mute" title="N/A"></i>
                                    {% endif %}
                                    <i class="bi bi-chevron-down float-end"></i>
                                </td>
                            </tr>
                            <tr class="collapse text-start" id="details-{{ loop.index }}">
                                <td colspan="4">
                                    <div class="card card-body bg-light">
                                        <p><strong>In-store Pickup:</strong>
                                            <span class="badge bg-{{ 'success' if store.is_pickup else 'secondary' }}">
                                                {{ 'Available' if store.is_pickup else 'Unavailable' }}
                                            </span>
                                        </p>
                                        <p><strong>Click & Collect:</strong>
                                            <span
                                                class="badge bg-{{ 'success' if store.is_click_collect else 'secondary' }}">
                                                {{ 'Available' if store.is_click_collect else 'Unavailable' }}
                                            </span>
                                        </p>
                                        <p><strong>Home Delivery:</strong>
                                            <span
                                                class="badge bg-{{ 'success' if store.is_home_delivery else 'secondary' }}">
                                                {{ 'Available' if store.is_home_delivery else 'Unavailable' }}
                                            </span>
                                        </p>
                                        <p><strong>Restock:</strong>
                                            {% if store.earliestRestock %}
                                            most likely between <u>{{ store.earliestRestock }}</u>
                                            and <u>{{ store.latestRestock }}</u> ({{ store.restockQuantity }} units)
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </p>
                                        <p><strong>Store Address:</strong> {{ store.address }} {{ store.zipCode }}</p>
                                        <p><strong>Store Website:</strong>
                                            {% if store.storePageUrl %}
                                            <a href="{{ store.storePageUrl }}" target="_blank">Visit Store Page</a>
                                            {% else %}
                                            N/A
                                            {% endif %}
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        <!-- end table -->

        {% endif %}
    </div>
</main>
<script src="{{ url_for('static', filename='stock.js') }}"></script>
{% endblock %}