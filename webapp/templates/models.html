{% extends "base.html" %}
{% block content %}
<main class="pb-5">
    <div class="container my-5">
        <div class="row">
            <h1 class="card-title text-center mb-4">All IKEA {{ q }} Products</h1>
            <h2 class="text-muted text-center mb-4">There are {{"{:,}".format(models|length)}} products from IKEA {{ q
                }} line
            </h2>
            <!-- TOC -->
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm" id="tableOfContents">
                    <div class="card-body">
                        <h3 class="card-title">Product Types</h3>
                        <div class="toc-scrollable">
                            <ul class="list-unstyled">
                                {% for group in models|groupby('product_type') %}
                                <li class="mb-2">
                                    <a href="#{{ group.grouper|lower|replace(' ', '-') }}"
                                        class="text-decoration-none fw-bold fs-6">&#10095; {{ group.grouper|capitalize
                                        }}</a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end TOC -->

            <!-- Main content -->
            <div class="col-md-9">
                <div class="card shadow-lg rounded-lg">
                    <div class="card-body">
                        {% for group in models|groupby('product_type') %}
                        <h3 id="{{ group.grouper|lower|replace(' ', '-') }}" class="type-groups mt-4 mb-3">{{
                            group.grouper|capitalize }}</h3>
                        <ul class="list-group mb-4">
                            {% for model in group.list %}
                            <li
                                class="list-group-item d-flex justify-content-between align-items-center hover-highlight">
                                <a href="{{ url_for('details', id=model.id, stag=model.stag) }}"
                                    class="text-black text-decoration-none">IKEA {{ model.name_w_type }}</a>
                                <span class="badge bg-primary rounded-pill">{{ model.product_num }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <!-- end Main content -->
        </div>
    </div>

    <!-- mobile bottom nav -->
    <nav class="mobile-bottom-nav d-md-none">
        <div class="mobile-bottom-nav__item m-2">
            <a href="javascript:history.back()" class="mobile-bottom-nav__item-content"><i
                    class="bi bi-arrow-left-square"></i> Back</a>
        </div>
        <div class="mobile-bottom-nav__item">
            <a href="#tableOfContents" class="mobile-bottom-nav__item-content"><i class="bi bi-card-list"></i>Product
                Types</a>
        </div>
    </nav>
    <!-- end mobile bottom nav -->
</main>
{% endblock %}
