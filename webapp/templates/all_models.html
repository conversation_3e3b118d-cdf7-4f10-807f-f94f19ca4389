{% extends "base.html" %}
{% block content %}
<main class="pb-5">
    <div class="container my-5">
        <div class="row justify-content-center">
            <h1 class="card-title text-center mb-4">All IKEA Models</h1>
            <h2 class="text-muted text-center mb-4">Number of IKEA models: {{"{:,}".format(models|length)}}</h2>
            <!-- TOC -->
            <div class="col-md-2 mb-4">
                <div class="card shadow-sm" id="tableOfContents">
                    <div class="card-body">
                        <h3 class="card-title">A - Z Index</h3>
                        <div class="toc-scrollable">
                            <ul class="list-unstyled">
                                {% for letter, group in models|groupby('0'|first|upper) %}
                                <li class="mb-2">
                                    <a href="#{{ letter }}" class="text-decoration-none fw-bold fs-6">&#10095; {{ letter
                                        }}</a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end TOC -->

            <!-- Main content -->
            <div class="col-md-6">
                <div class="card shadow-lg rounded-lg">
                    <div class="card-body">
                        {% for letter, group in models|groupby('0'|first|upper) %}
                        <h3 id="{{ letter }}" class="type-groups mt-4 mb-3">{{ letter }}</h3>
                        <ul class="list-group mb-4">
                            {% for model in group %}
                            <li
                                class="list-group-item d-flex justify-content-between align-items-center hover-highlight">
                                <a href="{{ url_for('model', model_name=model) }}" class="text-decoration-none">{{ model
                                    }}</a>
                            </li>
                            {% endfor %}
                        </ul>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <!-- end Main content -->
        </div>
    </div>

    <!-- mobile bottom nav -->
    <nav class="mobile-bottom-nav d-md-none">
        <div class="mobile-bottom-nav__item m-2">
            <a href="javascript:history.back()" class="mobile-bottom-nav__item-content"><i
                    class="bi bi-arrow-left-square"></i> Back</a>
        </div>
        <div class="mobile-bottom-nav__item">
            <a href="#tableOfContents" class="mobile-bottom-nav__item-content"><i
                    class="bi bi-card-list"></i>A - Z Index</a>
        </div>
    </nav>
    <!-- end mobile bottom nav -->
</main>
{% endblock %}
