{% set navigation_bar = [
    ('/', 'home', 'Home', 'bi-house-door'),
    ('/asis', 'asis', 'Bargain Finder', 'bi-tags'),
    ('/stock', 'stock', 'Stock Checker', 'bi-box-seam'),
    ('/models', 'models', 'IKEA Models', 'bi-grid-3x3-gap'),
    ('/about', 'about', 'About', 'bi-info-circle'),
] -%}

<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="/">
            <img src="{{ logo_url }}" alt="Logo" style="width:50px;">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                {% for href, id, caption, icon in navigation_bar %}
                <li class="nav-item">
                    <a class="nav-link {% if request.path == href %}active fw-bold border-bottom border-3 border-warning rounded-0 px-2 mx-1{% endif %}" 
                       href="{{ href }}" 
                       {% if request.path == href %}aria-current="page"{% endif %}>
                        <i class="bi {{ icon }} me-1"></i> {{ caption }}
                    </a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</nav>