{% extends "base.html" %}

{% block canonical_link %}
<link rel="canonical" href="{{ url_for('asis', _external=True) }}">
{% endblock %}

{% block content %}
<main class="pb-5">
    <div class="container my-5">
        <div class="col-12 col-md-8 mx-auto text-center mb-3">
            <h1 id="pageTitle" class="display-4 mt-3"><strong>IKEA AS-IS FINDER</strong></h1>
            <h2 id="pageSubtitle" class="lead">Find IKEA as-is section products in any stores worldwide</h2>
        </div>

        <!-- searchbar -->
        <div class="search-container mb-5">
            <div class="container">
                <form action="{{ url_for('asis') }}" id="asisSearchForm" method="GET" class="col-8 col-md-6 mx-auto">
                    <div class="form-group position-relative">
                        <div class="row g-2 align-items-center">
                            <div class="col-md-8">
                                <select name="country" class="form-select rounded-pill" aria-label="Select country">
                                    {% for region in ['North America', 'South America', 'Europe', 'Asia', 'Africa',
                                    'Oceania'] %}
                                    <optgroup label="{{ region }}">
                                        {% for code, country in countries.items() %}
                                        {% if country.region == region %}
                                        <option value="{{ code }}" {% if code==qCountry %}selected{% endif %}>
                                            {{ country.name }}
                                        </option>
                                        {% endif %}
                                        {% endfor %}
                                    </optgroup>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-4">
                                <button id="asisSearchButton" type="submit" class="btn btn-primary rounded-pill w-100"
                                    aria-label="Submit as is search">
                                    <span class="search-icon"><i class="bi bi-search"></i></span>
                                    <span class="loading-spinner d-none">
                                        <span class="spinner-border spinner-border-sm" role="status"
                                            aria-hidden="true"></span>
                                        <span class="visually-hidden">Loading...</span>
                                    </span>
                                </button>
                            </div>
                        </div>
                        <div id="searchError" class="invalid-feedback mt-2" style="display: none;"></div>
                    </div>
                </form>
            </div>
            <!-- faq -->
            {% if not all_asis_items %}
            <div class="row my-2">
                <div class="col-10 col-md-8 mx-auto my-3">
                    <!-- q1 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                How to use?
                            </button>
                        </h3>
                        <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="heading1">
                            <div class="accordion-body">
                                <ul>
                                    <li>Select your country</li>
                                    <li>View results showing as-is items available in every store</li>
                                    <li>Search an item and see which stores have it</li>
                                    <li>Click on item to reserve online and pick up in store.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- q2 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading2">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse2" aria-expanded="true" aria-controls="collapse2">
                                What is an 'IKEA As-Is Finder' and how does it help me get the best deals at IKEA
                                stores?
                            </button>
                        </h3>
                        <div id="collapse2" class="accordion-collapse collapse show" aria-labelledby="heading2">
                            <div class="accordion-body">
                                <p>As-is Finder helps you search for discounted furniture, appliances, and home
                                    goods in any IKEA stores' clearance sections <strong>worldwide</strong>. IKEA As-is
                                    section (bargain corner, circular hub, or second-hand section in other countries)
                                    offers floor models, returned items, discontinued products and slightly
                                    damaged merchandise at significant discounts - typically 30-80% off regular prices.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- q3 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading3">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse3" aria-expanded="true" aria-controls="collapse3">
                                How accurate is it?
                            </button>
                        </h3>
                        <div id="collapse3" class="accordion-collapse collapse show" aria-labelledby="heading3">
                            <div class="accordion-body">
                                <p>The data are updated daily to reflect the latest as-is stock.</p>
                            </div>
                        </div>
                    </div>

                    <!-- q4 -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="heading4">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapse4" aria-expanded="true" aria-controls="collapse4">
                                Technical support?
                            </button>
                        </h3>
                        <div id="collapse4" class="accordion-collapse collapse show" aria-labelledby="heading4">
                            <div class="accordion-body">
                                <p>Send me a message to <a class="text-primary font-weight-bold" href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            {% endif %}
            <!-- end faq -->
        </div>
        <!-- end searchbar -->

        {% if all_asis_items %}
        <!-- Content -->
        <div class="row">
            <!-- TOC -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm" id="tableOfContents">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="card-title mb-0">Store Locations</h3>
                            <button class="btn btn-sm btn-outline-primary" id="toggleDeselectAllStores">
                                Clear All
                            </button>
                        </div>
                        <div class="toc-scrollable">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <a href="#searchInput" class="text-decoration-none fw-bold fs-6">&#10095; Search</a>
                                </li>
                                {% for store_id, store in all_asis_items.items() %}
                                <li class="mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input store-checkbox" type="checkbox"
                                            id="store-{{ store_id }}" value="{{ store_id }}">
                                        <label class="form-check-label store-label fw-bold" for="store-{{ store_id }}">
                                            {{ store.displayNameAlternate }}
                                            <span
                                                class="{% if store.storeItems|length == 0 %}text-danger{% else %}text-success{% endif %}">
                                                ({{ store.storeItems|length }}
                                                {% if store.storeItems|length <= 1 %}item{% else %}items{% endif %})
                                                    </span>
                                        </label>
                                    </div>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-8">
                <div class="card shadow-lg rounded-lg">
                    <div class="card-body">
                        <div class="mb-5">
                            <div class="d-flex align-items-center gap-2">
                                <input type="text" id="searchInput" class="form-control" placeholder="Search items...">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                        id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-sort-down"></i> Sort
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" data-sort="price-asc">Price: Low to
                                                High</a></li>
                                        <li><a class="dropdown-item" href="#" data-sort="price-desc">Price: High to
                                                Low</a></li>
                                        <li>
                                            <hr class="dropdown-divider">
                                        </li>
                                        <li><a class="dropdown-item" href="#" data-sort="default">Newest</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div id="storesContainer">
                            {% for store_id, store in all_asis_items.items() %}
                            <div class="store-section mb-5" data-store-id="{{ store_id }}" style="display: none;">
                                <h2>{{ store.displayNameAlternate }}</h2>
                                <p>{{ store.displayAddress }}</p>

                                <div class="search-no-results alert alert-warning d-none">
                                    <i class="bi bi-search me-2"></i>
                                    No items found matching your search criteria for this store
                                </div>

                                {% if store.storeItems %}
                                <div class="row row-cols-2 row-cols-md-3 g-2">
                                    {% for item in store.storeItems %}
                                    <div class="col item-card" data-original-index="{{ loop.index0 }}">
                                        <div class="card h-100 shadow">
                                            <a href="{{ item.get('item_url', '') }}"
                                                class="text-decoration-none text-dark" target="_blank"
                                                rel="noopener noreferrer">
                                                <div class="ratio ratio-1x1">
                                                    <img src="{{ item.heroImage }}"
                                                        class="card-img-top object-fit-cover" alt="{{ item.title }}"
                                                        loading="lazy">
                                                </div>
                                                <div class="card-body">
                                                    <h5 class="card-title mb-1">{{ item.title }}</h5>
                                                    <p class="card-text mb-1">{{ item.description }}</p>
                                                    <p class="card-text mb-1">
                                                        <strong>Price:</strong>
                                                        <span class="text-success">{{ item.currency }} {{ item.price
                                                            }}</span>
                                                        <del class="ms-2">{{ item.currency }} {{
                                                            item.articlesPrice}}</del>
                                                    </p>
                                                    <p class="card-text mb-1"><strong>Discount Reason:</strong>
                                                        {{item.reasonDiscount }}</p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <h5 class="text-muted">No As Is items for this store</h5>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            <!-- end Main content -->
        </div>
        {% elif qCountry %}
        <h3 class="text-danger mt-3">Invalid request (not a valid country)</h3>
        {% endif %}
        <!-- end Content -->
    </div>

    <!-- mobile bottom nav -->
    {% if all_asis_items %}
    <nav class="mobile-bottom-nav d-md-none">
        <div class="mobile-bottom-nav__item m-2">
            <a href="javascript:history.back()" class="mobile-bottom-nav__item-content"><i
                    class="bi bi-arrow-left-square"></i> Back</a>
        </div>
        <div class="mobile-bottom-nav__item">
            <a href="#tableOfContents" class="mobile-bottom-nav__item-content"><i class="bi bi-card-list"></i>Store
                Locations</a>
        </div>
    </nav>
    {% endif %}
    <!-- end mobile bottom nav -->
</main>

<script src="{{ url_for('static', filename='asis.js') }}"></script>
{% endblock %}