{% extends "base.html" %}
{% block canonical_link %}
<link rel="canonical" href="{{ url_for('search', _external=True) }}">
{% endblock %}

{% block content %}
<main class="pb-5">
    <div class="container">
        {% include 'searchbar.html' %}
        <div id="result" class="result-container mt-3">
            {% if search_results %}
            <h3 class="mb-3 text-primary text-center">Found {{ total }} result{{ "s" if total != 1 else "" }} for {{
                "article number" if search_type == "anum" else search_type }} <strong>"{{ q }}"</strong></h3>

            {% if part_data %}
            <div class="row justify-content-center mb-4">
                <div class="col-md-8 col-lg-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="row g-0 align-items-center p-3">
                            <div class="col-4">
                                <img src="{{ part_data.get('img', '') }}" class="img-fluid rounded p-2"
                                    style="width: auto; height: 160px; object-fit: contain;"
                                    alt="IKEA part number {{ q }} image">
                            </div>
                            <div class="col-8">
                                <div class="card-body">
                                    <h5 class="card-title text-primary mb-2">Part number: {{ q }}</h5>
                                    <p class="card-text mb-0">{{ part_data.get('name') }}</p>
                                    <p class="card-text mb-0">{{ part_data.get('description') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm rounded-3 border-0">
                        <ul class="list-group list-group-flush">
                            {% for result in search_results %}
                            <li class="list-group-item list-group-item-action p-3 hover-bg-light">
                                <a href="{{ url_for('details', id=result.id, stag=result.stag, q=q) }}"
                                    class="text-decoration-none">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="flex-shrink-0">
                                            <div class="ratio ratio-1x1" style="width: 100px;">
                                                <img src="{{ result.thumbnail }}"
                                                    alt="IKEA {{ result.name }} thumbnail image"
                                                    class="img-fluid rounded object-fit-cover">
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1 text-primary">IKEA {{ result.name_w_type | safe }}</h5>
                                            <p class="mb-0 text-muted small">Click for details</p>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>

            <nav class="mt-4" aria-label="Page navigation">
                <ul class="pagination justify-content-center flex-wrap gap-2">
                    {% set current_page = request.args.get('page', default=1)|int %}
                    {% if current_page != 1 %}
                    <li class="page-item">
                        <a class="page-link fw-bold rounded-pill"
                            href="{{ url_for('search', q=q, page=1, searchType=search_type) }}">First</a>
                    </li>
                    {% endif %}
                    {% if current_page > 1 %}
                    <li class="page-item">
                        <a class="page-link fw-bold rounded-pill"
                            href="{{ url_for('search', q=q, page=current_page-1, searchType=search_type) }}">Prev</a>
                    </li>
                    {% endif %}
                    {% for page_num in range(1, total_pages + 1) %}
                    {% if page_num <= current_page + 5 and page_num>= current_page - 2 %}
                        <li class="page-item {% if page_num == current_page %}active{% endif %}">
                            <a class="page-link rounded-pill"
                                href="{{ url_for('search', q=q, page=page_num, searchType=search_type) }}">{{ page_num
                                }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                        {% if current_page < total_pages %} <li class="page-item">
                            <a class="page-link fw-bold rounded-pill"
                                href="{{ url_for('search', q=q, page=current_page+1, searchType=request.args.get('searchType')) }}">Next</a>
                            </li>
                            {% endif %}
                            {% if current_page != total_pages %}
                            <li class="page-item">
                                <a class="page-link fw-bold rounded-pill"
                                    href="{{ url_for('search', q=q, page=total_pages, searchType=search_type) }}">Last</a>
                            </li>
                            {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow-sm rounded-3 border-0">
                        <div class="card-body text-center p-5">
                            <h3 class="card-title text-primary mb-3">No results found</h3>
                            <p class="card-text text-muted mb-0">
                                No matches found for {{ "article number" if search_type == "anum" else search_type }}
                                "{{ q }}".
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const searchTermWords = "{{ q }}".split(" ");
        const context = document.querySelectorAll('.list-group-item');

        context.forEach(item => {
            const markObj = new Mark(item);
            searchTermWords.forEach(word => {
                markObj.mark(word, {
                    element: "span",
                    className: "bg-warning"
                });
            });
        });
    });
</script>
{% endblock %}