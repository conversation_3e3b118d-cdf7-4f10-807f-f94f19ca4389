document.addEventListener('DOMContentLoaded', function () {
    const state = {
        currentSort: 'default',
        itemsCache: new Map(),
        originalOrder: new Map(),
        isSearchActive: false,
        selectedStores: new Set()
    };

    const searchInput = document.getElementById('searchInput');
    const storeCheckboxes = document.querySelectorAll('.store-checkbox');
    const storeSections = document.querySelectorAll('.store-section');
    const toggleAllButton = document.getElementById('toggleDeselectAllStores');

    // search bar loading spinner 
    document.getElementById('asisSearchForm').addEventListener('submit', function (e) {
        const button = document.getElementById('asisSearchButton');
        const searchIcon = button.querySelector('.search-icon');
        const loadingSpinner = button.querySelector('.loading-spinner');

        searchIcon.classList.add('d-none');
        loadingSpinner.classList.remove('d-none');
        button.disabled = true;
    });

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function cacheStoreItems(storeId) {
        if (!state.itemsCache.has(storeId)) {
            const storeSection = document.querySelector(`.store-section[data-store-id="${storeId}"]`);
            if (!storeSection) return null;

            const items = Array.from(storeSection.querySelectorAll('.item-card')).map((item, index) => ({
                element: item,
                price: parseFloat(item.querySelector('.text-success').textContent.trim().split(' ')[1]) || 0,
                originalIndex: index,
                searchText: item.textContent.toLowerCase()
            }));

            state.itemsCache.set(storeId, items);
            state.originalOrder.set(storeId, [...items]);
        }
        return state.itemsCache.get(storeId);
    }

    function updateStoreVisibility() {
        storeSections.forEach(section => {
            const storeId = section.dataset.storeId;
            const isVisible = state.selectedStores.has(storeId);
            section.style.display = isVisible ? 'block' : 'none';

            if (isVisible) {
                cacheStoreItems(storeId);
            }
        });

        sortItems(state.currentSort);
    }

    function initializeFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const storeParam = urlParams.get('store');
        if (storeParam) {
            const storeIds = storeParam.split(',');
            storeIds.forEach(storeId => {
                const checkbox = document.querySelector(`.store-checkbox[value="${storeId}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    state.selectedStores.add(storeId);
                }
            });
        }
        
        const filterParam = urlParams.get('filter');
        if (filterParam) {
            searchInput.value = decodeURIComponent(filterParam);
            handleSearch();
        }

        updateStoreVisibility();
    }

    function updateURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const country = urlParams.get('country');

        const newParams = new URLSearchParams();
        if (country) {
            newParams.set('country', country);
        }
        
        if (state.selectedStores.size > 0) {
            newParams.set('store', Array.from(state.selectedStores).join(','));
        }

        if (searchInput.value) {
            newParams.set('filter', encodeURIComponent(searchInput.value));
        }

        // Update URL without reloading the page
        const newURL = `${window.location.pathname}?${newParams.toString()}`;
        window.history.pushState({ path: newURL }, '', newURL);
    }

    function handleStoreCheckbox(e) {
        const checkbox = e.target;
        const storeId = checkbox.value;

        if (checkbox.checked) {
            state.selectedStores.add(storeId);
            
            updateStoreVisibility();
            
            const storeSection = document.querySelector(`.store-section[data-store-id="${storeId}"]`);
            if (storeSection) {
                storeSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        } else {
            state.selectedStores.delete(storeId);
            updateStoreVisibility();
        }

        updateURL();
    }

    function deselectAllStores() {
        storeCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            state.selectedStores.delete(checkbox.value);
        });
        
        updateStoreVisibility();
        updateURL(); 
    }

    function sortItems(sortType) {
        state.selectedStores.forEach(storeId => {
            sortStoreItems(storeId, sortType);
        });

        state.currentSort = sortType;
        updateSortButtonText(sortType);
    }

    function getVisibleItems(items, searchTerm = '') {
        if (!searchTerm) {
            return items;
        }
        return items.filter(item => item.searchText.includes(searchTerm));
    }

    function sortStoreItems(storeId, sortType) {
        const items = state.itemsCache.get(storeId);
        if (!items) return;

        const searchTerm = searchInput.value.toLowerCase();
        const visibleItems = getVisibleItems(items, searchTerm);
        
        const hasVisibleItems = visibleItems.length !== 0;
        const section = document.querySelector(`.store-section[data-store-id="${storeId}"]`);
        const noResultsWarning = section.querySelector('.search-no-results');
        if (searchTerm && !hasVisibleItems) {
            noResultsWarning.classList.remove('d-none');
        } else {
            noResultsWarning.classList.add('d-none');
        }

        const sortedItems = [...visibleItems];
        switch (sortType) {
            case 'price-asc':
                sortedItems.sort((a, b) => a.price - b.price);
                break;
            case 'price-desc':
                sortedItems.sort((a, b) => b.price - a.price);
                break;
            case 'default':
                sortedItems.sort((a, b) => a.originalIndex - b.originalIndex);
                break;
        }

        const container = document.querySelector(`.store-section[data-store-id="${storeId}"] .row`);
        const fragment = document.createDocumentFragment();

        items.forEach(item => {
            item.element.style.display = 'none';
        });

        sortedItems.forEach(item => {
            item.element.style.display = '';
            fragment.appendChild(item.element);
        });

        if (container) {
            container.innerHTML = '';
            container.appendChild(fragment);
        }
    }

    function updateSortButtonText(sortType) {
        const sortDropdown = document.getElementById('sortDropdown');
        const sortText = {
            'price-asc': 'Price: Low to High',
            'price-desc': 'Price: High to Low',
            'default': 'Newest'
        };
        sortDropdown.innerHTML = `<i class="bi bi-sort-down"></i> ${sortText[sortType] || 'Newest'}`;
    }

    const handleSearch = debounce(function () {
        const searchTerm = searchInput.value.toLowerCase();
        state.isSearchActive = searchTerm !== '';

        state.selectedStores.forEach(storeId => {
            const items = state.itemsCache.get(storeId);
            if (!items) return;

            let hasVisibleItems = false;
            
            items.forEach(item => {
                const isVisible = !searchTerm || item.searchText.includes(searchTerm);
                item.element.style.display = isVisible ? '' : 'none';
                if (isVisible) hasVisibleItems = true;
            });
        });

        sortItems(state.currentSort);
        updateURL();
    }, 360);

    storeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleStoreCheckbox);
    });

    toggleAllButton.addEventListener('click', deselectAllStores);

    document.querySelector('.dropdown-menu').addEventListener('click', function (e) {
        if (!e.target.matches('.dropdown-item[data-sort]')) return;
        
        e.preventDefault();
        const sortType = e.target.dataset.sort;
        sortItems(sortType);
    });

    searchInput.addEventListener('input', handleSearch);

    window.addEventListener('popstate', function () {
        initializeFromURL();
    });

    initializeFromURL();
});