document.addEventListener("DOMContentLoaded", function() {
    // QnA submit
    function resetModalDisplay() {
        document.querySelector('.modal-body').style.display = 'block';
        document.querySelector('.modal-footer').style.display = 'block';
        document.querySelector('.modal-result').style.display = 'none';
    }

    const questionModal = document.getElementById('questionModal');
    questionModal.addEventListener('show.bs.modal', resetModalDisplay);

    // send question
    document.getElementById('submitQuestionBtn').addEventListener('click', function() {
        var questionForm = document.getElementById('questionForm');
        questionForm.classList.add('was-validated');
        
        if (!questionForm.checkValidity()) {
            return;
        }

        var question = document.getElementById('qnaQuestion').value;
        var name = document.getElementById('qnaQuestionName').value;
        var email = document.getElementById('qnaQuestionEmail').value;
        var fid = document.querySelector('[data-fid]').getAttribute('data-fid');

        var formData = new FormData();
        formData.append('furniture_id', fid);
        formData.append('user', name);
        formData.append('text', question);
        formData.append('email', email);
        formData.append('currentUrl', window.location.href);

        fetch('/add_question', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            document.getElementById('qnaQuestion').value = '';
            document.getElementById('qnaQuestionName').value = '';
            document.getElementById('qnaQuestionEmail').value = '';

            questionModal.querySelector('.modal-body').style.display = 'none';
            questionModal.querySelector('.modal-footer').style.display = 'none';
            questionModal.querySelector('.modal-result').innerHTML = '<div class="alert alert-success">Question has been posted.</div>';
            questionModal.querySelector('.modal-result').style.display = 'block';
        })
        .catch((error) => {
            console.error('Error:', error);
            document.querySelector('.modal-result').innerHTML = '<div class="alert alert-danger">An error occurred, please refresh and submit again.</div>';
            document.querySelector('.modal-result').style.display = 'block';
        });
    });

    // show answer modal
    const commentModal = document.getElementById('commentModal');
    commentModal.addEventListener('show.bs.modal', function (event) {
        resetModalDisplay();

        var button = event.relatedTarget;
        var questionText = button.getAttribute('data-question');
        var qid = button.getAttribute('data-qid');
        var asker = button.getAttribute('data-asker');
        var nameInitial = asker[0].toUpperCase();
        var qtime = button.getAttribute('data-qtime');

        this.querySelector('#questionText').innerHTML = questionText.replace(/\n/g, '<br/>');
        this.querySelector('#qid').value = qid;
        this.querySelector('#asker').textContent = asker;
        this.querySelector('#qtime').textContent = qtime;
        this.querySelector('#userAvatarComment').textContent = nameInitial;
    });

    // send answer
    document.getElementById('submitCommentBtn').addEventListener('click', function() {
        var commentForm = document.getElementById('commentForm');
        commentForm.classList.add('was-validated');
        
        if (!commentForm.checkValidity()) {
            return;
        }

        var comment = document.getElementById('commentText').value;
        var name = document.getElementById('commentName').value;
        var email = document.getElementById('commentEmail').value;
        var questionId = document.getElementById('qid').value;
        var fid = document.querySelector('[data-fid]').getAttribute('data-fid');

        var formData = new FormData();
        formData.append('furniture_id', fid);
        formData.append('question_id', questionId);
        formData.append('user', name);
        formData.append('text', comment);
        formData.append('email', email);
        formData.append('currentUrl', window.location.href);

        fetch('/add_answer', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            document.getElementById('commentText').value = '';
            document.getElementById('commentName').value = '';
            document.getElementById('commentEmail').value = '';
            
            commentModal.querySelector('.modal-body').style.display = 'none';
            commentModal.querySelector('.modal-footer').style.display = 'none';
            commentModal.querySelector('.modal-result').innerHTML = '<div class="alert alert-success">Comment has been posted.</div>';
            commentModal.querySelector('.modal-result').style.display = 'block';
        })
        .catch((error) => {
            console.error('Error:', error);
            commentModal.querySelector('.modal-result').innerHTML = '<div class="alert alert-danger">An error occurred, please refresh and submit again.</div>';
            commentModal.querySelector('.modal-result').style.display = 'block';
        });
    });

    questionModal.addEventListener('hidden.bs.modal', function () {
        document.getElementById('questionForm').classList.remove('was-validated');
    });

    commentModal.addEventListener('hidden.bs.modal', function () {
        document.getElementById('commentForm').classList.remove('was-validated');
    });
});