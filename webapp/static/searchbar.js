const searchInput = document.getElementById("searchInput");
const searchAutoComplete = document.getElementById("searchAutocomplete");
const searchForm = document.getElementById("searchForm");
const typeRadio = document.getElementById("typeRadio");
const partRadio = document.getElementById("partRadio");
const anumRadio = document.getElementById("anumRadio");

// Search autocomplete
function clearSearchAutoCompleteResults() {
    searchAutoComplete.innerHTML = "";
    searchAutoComplete.classList.remove("show");
}

document.addEventListener("click", function (e) {
    if (!searchAutoComplete.contains(e.target) && e.target !== searchInput) {
        clearSearchAutoCompleteResults();
    }
});

let currentIndex = -1;
searchInput.addEventListener("keydown", function (e) {
    const items = searchAutoComplete.getElementsByClassName("dropdown-item");
    
    function updateActiveItem(index) {
        Array.from(items).forEach((item, i) => {
            item.classList.toggle("active", i === index);
        });
        if (index >= 0) {
            searchInput.value = items[index].textContent;
        }
    }

    switch(e.key) {
        case 'ArrowUp':
            currentIndex = Math.max(-1, currentIndex - 1);
            updateActiveItem(currentIndex);
            e.preventDefault();
            break;
        case 'ArrowDown':
            currentIndex = Math.min(items.length - 1, currentIndex + 1);
            updateActiveItem(currentIndex);
            e.preventDefault();
            break;
        case 'Escape':
            clearSearchAutoCompleteResults();
            break;
    }
});

function handleSearch(e, collectionName) {
    const searchTerm = e.target.value.trim();
    clearSearchAutoCompleteResults();
    
    if (searchTerm === "") return;

    client.collections(collectionName).documents().search({
        q: searchTerm,
        query_by: "name",
    })
    .then(function (response) {
        if (response.hits.length === 0) {
            searchAutoComplete.innerHTML = '<span class="dropdown-item">No results</span>';
        } else {
            const searchTermWords = searchTerm.split(' ');
            response.hits.slice(0, 10).forEach(hit => {
                const resultItem = document.createElement("span");
                resultItem.className = "dropdown-item";
                resultItem.textContent = hit.document.name;
                
                const toBeHighlighted = new Mark(resultItem);
                searchTermWords.forEach(word => {
                    toBeHighlighted.mark(word, { "element": "span", "className": "bg-warning" });
                });
                
                resultItem.addEventListener("click", function () {
                    searchInput.value = resultItem.textContent;
                    searchForm.submit();
                });
                searchAutoComplete.appendChild(resultItem);
            });
        }
        searchAutoComplete.classList.add("show");
    })
    .catch(function (error) {
        console.error(error);
    });
}

let debounceTimeoutId;
searchInput.addEventListener("input", function (e) {
    clearTimeout(debounceTimeoutId);
    debounceTimeoutId = setTimeout(() => {
        const selectedRadio = document.querySelector('input[name="searchType"]:checked');
        const collectionName = selectedRadio.value === 'type' ? 'p_type' : 
                               selectedRadio.value === 'part' ? 'parts' : 'anums';
        handleSearch(e, collectionName);
    }, 360);
});

function validateSearchTerm() {
    const searchTerm = searchInput.value.trim();
    if (searchTerm === "") {
        showErrorModal("Search term cannot be empty");
        return false;
    } else if (searchTerm.length < 2) {
        showErrorModal("Too short! Search term must be at least 2 characters long");
        return false;
    } else if (searchTerm.length > 60) {
        showErrorModal("Too long! Search term must be at most 60 characters long");
        return false;
    }
    return true;
}

function showErrorModal(message) {
    document.getElementById("errorModalBody").textContent = message;
    new bootstrap.Modal(document.getElementById("errorModal")).show();
}

document.addEventListener("DOMContentLoaded", function() {
    const errorModal = document.getElementById("errorModal");
    errorModal.addEventListener("hidden.bs.modal", function () {
        searchInput.focus();
    });
});