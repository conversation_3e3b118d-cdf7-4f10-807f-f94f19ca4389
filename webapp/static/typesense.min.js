!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Typesense=e()}(function(){return function i(a,s,u){function l(t,e){if(!s[t]){if(!a[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(c)return c(t,!0);var n=new Error("Cannot find module '"+t+"'");throw n.code="MODULE_NOT_FOUND",n}var o=s[t]={exports:{}};a[t][0].call(o.exports,function(e){return l(a[t][1][e]||e)},o,o.exports,i,a,s,u)}return s[t].exports}for(var c="function"==typeof require&&require,e=0;e<u.length;e++)l(u[e]);return l}({1:[function(e,t,r){t.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},t.exports.default=t.exports,t.exports.__esModule=!0},{}],2:[function(e,t,r){var n=e("./arrayLikeToArray.js");t.exports=function(e){if(Array.isArray(e))return n(e)},t.exports.default=t.exports,t.exports.__esModule=!0},{"./arrayLikeToArray.js":1}],3:[function(e,t,r){t.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},t.exports.default=t.exports,t.exports.__esModule=!0},{}],4:[function(e,t,r){function u(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}t.exports=function(s){return function(){var e=this,a=arguments;return new Promise(function(t,r){var n=s.apply(e,a);function o(e){u(n,t,r,o,i,"next",e)}function i(e){u(n,t,r,o,i,"throw",e)}o(void 0)})}},t.exports.default=t.exports,t.exports.__esModule=!0},{}],5:[function(e,t,r){t.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},t.exports.default=t.exports,t.exports.__esModule=!0},{}],6:[function(e,n,t){var i=e("./setPrototypeOf.js"),o=e("./isNativeReflectConstruct.js");function a(e,t,r){return o()?n.exports=a=Reflect.construct:n.exports=a=function(e,t,r){var n=[null];n.push.apply(n,t);var o=new(Function.bind.apply(e,n));return r&&i(o,r.prototype),o},n.exports.default=n.exports,n.exports.__esModule=!0,a.apply(null,arguments)}n.exports=a,n.exports.default=n.exports,n.exports.__esModule=!0},{"./isNativeReflectConstruct.js":12,"./setPrototypeOf.js":16}],16:[function(e,r,t){function n(e,t){return r.exports=n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},r.exports.default=r.exports,r.exports.__esModule=!0,n(e,t)}r.exports=n,r.exports.default=r.exports,r.exports.__esModule=!0},{}],12:[function(e,t,r){t.exports=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}},t.exports.default=t.exports,t.exports.__esModule=!0},{}],7:[function(e,t,r){function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}t.exports=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e},t.exports.default=t.exports,t.exports.__esModule=!0},{}],8:[function(e,t,r){function n(e){return t.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},t.exports.default=t.exports,t.exports.__esModule=!0,n(e)}t.exports=n,t.exports.default=t.exports,t.exports.__esModule=!0},{}],9:[function(e,t,r){var n=e("./setPrototypeOf.js");t.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)},t.exports.default=t.exports,t.exports.__esModule=!0},{"./setPrototypeOf.js":16}],10:[function(e,t,r){t.exports=function(e){return e&&e.__esModule?e:{default:e}},t.exports.default=t.exports,t.exports.__esModule=!0},{}],11:[function(e,t,r){t.exports=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},t.exports.default=t.exports,t.exports.__esModule=!0},{}],13:[function(e,t,r){t.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},t.exports.default=t.exports,t.exports.__esModule=!0},{}],14:[function(e,t,r){t.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.default=t.exports,t.exports.__esModule=!0},{}],15:[function(e,t,r){var n=e("@babel/runtime/helpers/typeof").default,o=e("./assertThisInitialized.js");t.exports=function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},t.exports.default=t.exports,t.exports.__esModule=!0},{"./assertThisInitialized.js":3,"@babel/runtime/helpers/typeof":18}],18:[function(e,t,r){function n(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?t.exports=n=function(e){return typeof e}:t.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.default=t.exports,t.exports.__esModule=!0,n(e)}t.exports=n,t.exports.default=t.exports,t.exports.__esModule=!0},{}],17:[function(e,t,r){var n=e("./arrayWithoutHoles.js"),o=e("./iterableToArray.js"),i=e("./unsupportedIterableToArray.js"),a=e("./nonIterableSpread.js");t.exports=function(e){return n(e)||o(e)||i(e)||a()},t.exports.default=t.exports,t.exports.__esModule=!0},{"./arrayWithoutHoles.js":2,"./iterableToArray.js":13,"./nonIterableSpread.js":14,"./unsupportedIterableToArray.js":19}],19:[function(e,t,r){var n=e("./arrayLikeToArray.js");t.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},t.exports.default=t.exports,t.exports.__esModule=!0},{"./arrayLikeToArray.js":1}],20:[function(e,t,r){var n=e("./getPrototypeOf.js"),o=e("./setPrototypeOf.js"),i=e("./isNativeFunction.js"),a=e("./construct.js");function s(e){var r="function"==typeof Map?new Map:void 0;return t.exports=s=function(e){if(null===e||!i(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return a(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},t.exports.default=t.exports,t.exports.__esModule=!0,s(e)}t.exports=s,t.exports.default=t.exports,t.exports.__esModule=!0},{"./construct.js":6,"./getPrototypeOf.js":8,"./isNativeFunction.js":11,"./setPrototypeOf.js":16}],21:[function(e,t,r){t.exports=e("regenerator-runtime")},{"regenerator-runtime":57}],57:[function(e,t,r){var n=function(a){"use strict";var f,e=Object.prototype,c=e.hasOwnProperty,t="function"==typeof Symbol?Symbol:{},o=t.iterator||"@@iterator",r=t.asyncIterator||"@@asyncIterator",n=t.toStringTag||"@@toStringTag";function i(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{i({},"")}catch(e){i=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i,a,s,u,o=t&&t.prototype instanceof v?t:v,l=Object.create(o.prototype),c=new _(n||[]);return l._invoke=(i=e,a=r,s=c,u=p,function(e,t){if(u===b)throw new Error("Generator is already running");if(u===y){if("throw"===e)throw t;return S()}for(s.method=e,s.arg=t;;){var r=s.delegate;if(r){var n=function e(t,r){var n=t.iterator[r.method];if(n===f){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=f,e(t,r),"throw"===r.method))return m;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var o=h(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=f),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}(r,s);if(n){if(n===m)continue;return n}}if("next"===s.method)s.sent=s._sent=s.arg;else if("throw"===s.method){if(u===p)throw u=y,s.arg;s.dispatchException(s.arg)}else"return"===s.method&&s.abrupt("return",s.arg);u=b;var o=h(i,a,s);if("normal"===o.type){if(u=s.done?y:d,o.arg===m)continue;return{value:o.arg,done:s.done}}"throw"===o.type&&(u=y,s.method="throw",s.arg=o.arg)}}),l}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var p="suspendedStart",d="suspendedYield",b="executing",y="completed",m={};function v(){}function u(){}function l(){}var g={};i(g,o,function(){return this});var w=Object.getPrototypeOf,C=w&&w(w(T([])));C&&C!==e&&c.call(C,o)&&(g=C);var x=l.prototype=v.prototype=Object.create(g);function R(e){["next","throw","return"].forEach(function(t){i(e,t,function(e){return this._invoke(t,e)})})}function E(u,l){var t;this._invoke=function(r,n){function e(){return new l(function(e,t){!function t(e,r,n,o){var i=h(u[e],u,r);if("throw"!==i.type){var a=i.arg,s=a.value;return s&&"object"==typeof s&&c.call(s,"__await")?l.resolve(s.__await).then(function(e){t("next",e,n,o)},function(e){t("throw",e,n,o)}):l.resolve(s).then(function(e){a.value=e,n(a)},function(e){return t("throw",e,n,o)})}o(i.arg)}(r,n,e,t)})}return t=t?t.then(e,e):e()}}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function T(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(c.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=f,e.done=!0,e};return n.next=n}}return{next:S}}function S(){return{value:f,done:!0}}return i(x,"constructor",u.prototype=l),i(l,"constructor",u),u.displayName=i(l,n,"GeneratorFunction"),a.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===u||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,i(e,n,"GeneratorFunction")),e.prototype=Object.create(x),e},a.awrap=function(e){return{__await:e}},R(E.prototype),i(E.prototype,r,function(){return this}),a.AsyncIterator=E,a.async=function(e,t,r,n,o){void 0===o&&(o=Promise);var i=new E(s(e,t,r,n),o);return a.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},R(x),i(x,n,"Generator"),i(x,o,function(){return this}),i(x,"toString",function(){return"[object Generator]"}),a.keys=function(r){var n=[];for(var e in r)n.push(e);return n.reverse(),function e(){for(;n.length;){var t=n.pop();if(t in r)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=T,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=f,this.done=!1,this.delegate=null,this.method="next",this.arg=f,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=f)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var n=this;function e(e,t){return i.type="throw",i.arg=r,n.next=e,t&&(n.method="next",n.arg=f),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var o=this.tryEntries[t],i=o.completion;if("root"===o.tryLoc)return e("end");if(o.tryLoc<=this.prev){var a=c.call(o,"catchLoc"),s=c.call(o,"finallyLoc");if(a&&s){if(this.prev<o.catchLoc)return e(o.catchLoc,!0);if(this.prev<o.finallyLoc)return e(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return e(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return e(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&c.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n,o=r.completion;return"throw"===o.type&&(n=o.arg,O(r)),n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:T(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=f),m}},a}("object"==typeof t?t.exports:{});try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},{}],22:[function(e,t,r){t.exports=e("./lib/axios")},{"./lib/axios":24}],24:[function(e,t,r){"use strict";var n=e("./utils"),o=e("./helpers/bind"),i=e("./core/Axios"),a=e("./core/mergeConfig");function s(e){var t=new i(e),r=o(i.prototype.request,t);return n.extend(r,i.prototype,t),n.extend(r,t),r}var u=s(e("./defaults"));u.Axios=i,u.create=function(e){return s(a(u.defaults,e))},u.Cancel=e("./cancel/Cancel"),u.CancelToken=e("./cancel/CancelToken"),u.isCancel=e("./cancel/isCancel"),u.all=function(e){return Promise.all(e)},u.spread=e("./helpers/spread"),u.isAxiosError=e("./helpers/isAxiosError"),t.exports=u,t.exports.default=u},{"./cancel/Cancel":25,"./cancel/CancelToken":26,"./cancel/isCancel":27,"./core/Axios":28,"./core/mergeConfig":34,"./defaults":37,"./helpers/bind":38,"./helpers/isAxiosError":43,"./helpers/spread":47,"./utils":49}],23:[function(e,t,r){"use strict";var h=e("./../utils"),p=e("./../core/settle"),d=e("./../helpers/cookies"),b=e("./../helpers/buildURL"),y=e("../core/buildFullPath"),m=e("./../helpers/parseHeaders"),v=e("./../helpers/isURLSameOrigin"),g=e("../core/createError");t.exports=function(f){return new Promise(function(r,n){var o=f.data,i=f.headers,a=f.responseType;h.isFormData(o)&&delete i["Content-Type"];var e,t,s=new XMLHttpRequest;f.auth&&(e=f.auth.username||"",t=f.auth.password?unescape(encodeURIComponent(f.auth.password)):"",i.Authorization="Basic "+btoa(e+":"+t));var u,l=y(f.baseURL,f.url);function c(){var e,t;s&&(e="getAllResponseHeaders"in s?m(s.getAllResponseHeaders()):null,t={data:a&&"text"!==a&&"json"!==a?s.response:s.responseText,status:s.status,statusText:s.statusText,headers:e,config:f,request:s},p(r,n,t),s=null)}s.open(f.method.toUpperCase(),b(l,f.params,f.paramsSerializer),!0),s.timeout=f.timeout,"onloadend"in s?s.onloadend=c:s.onreadystatechange=function(){s&&4===s.readyState&&(0!==s.status||s.responseURL&&0===s.responseURL.indexOf("file:"))&&setTimeout(c)},s.onabort=function(){s&&(n(g("Request aborted",f,"ECONNABORTED",s)),s=null)},s.onerror=function(){n(g("Network Error",f,null,s)),s=null},s.ontimeout=function(){var e="timeout of "+f.timeout+"ms exceeded";f.timeoutErrorMessage&&(e=f.timeoutErrorMessage),n(g(e,f,f.transitional&&f.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",s)),s=null},!h.isStandardBrowserEnv()||(u=(f.withCredentials||v(l))&&f.xsrfCookieName?d.read(f.xsrfCookieName):void 0)&&(i[f.xsrfHeaderName]=u),"setRequestHeader"in s&&h.forEach(i,function(e,t){void 0===o&&"content-type"===t.toLowerCase()?delete i[t]:s.setRequestHeader(t,e)}),h.isUndefined(f.withCredentials)||(s.withCredentials=!!f.withCredentials),a&&"json"!==a&&(s.responseType=f.responseType),"function"==typeof f.onDownloadProgress&&s.addEventListener("progress",f.onDownloadProgress),"function"==typeof f.onUploadProgress&&s.upload&&s.upload.addEventListener("progress",f.onUploadProgress),f.cancelToken&&f.cancelToken.promise.then(function(e){s&&(s.abort(),n(e),s=null)}),o=o||null,s.send(o)})}},{"../core/buildFullPath":30,"../core/createError":31,"./../core/settle":35,"./../helpers/buildURL":39,"./../helpers/cookies":41,"./../helpers/isURLSameOrigin":44,"./../helpers/parseHeaders":46,"./../utils":49}],49:[function(e,t,r){"use strict";var o=e("./helpers/bind"),n=Object.prototype.toString;function i(e){return"[object Array]"===n.call(e)}function a(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function u(e){if("[object Object]"!==n.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function l(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}t.exports={isArray:i,isArrayBuffer:function(e){return"[object ArrayBuffer]"===n.call(e)},isBuffer:function(e){return null!==e&&!a(e)&&null!==e.constructor&&!a(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){var t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer;return t},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:u,isUndefined:a,isDate:function(e){return"[object Date]"===n.call(e)},isFile:function(e){return"[object File]"===n.call(e)},isBlob:function(e){return"[object Blob]"===n.call(e)},isStream:function(e){return s(e)&&(t=e.pipe,"[object Function]"===n.call(t));var t},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:l,merge:function r(){var n={};function e(e,t){u(n[t])&&u(e)?n[t]=r(n[t],e):u(e)?n[t]=r({},e):i(e)?n[t]=e.slice():n[t]=e}for(var t=0,o=arguments.length;t<o;t++)l(arguments[t],e);return n},extend:function(r,e,n){return l(e,function(e,t){r[t]=n&&"function"==typeof e?o(e,n):e}),r},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}}},{"./helpers/bind":38}],39:[function(e,t,r){"use strict";var a=e("./../utils");function s(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(e,t,r){if(!t)return e;var n,o,i=r?r(t):a.isURLSearchParams(t)?t.toString():(n=[],a.forEach(t,function(e,t){null!=e&&(a.isArray(e)?t+="[]":e=[e],a.forEach(e,function(e){a.isDate(e)?e=e.toISOString():a.isObject(e)&&(e=JSON.stringify(e)),n.push(s(t)+"="+s(e))}))}),n.join("&"));return i&&(-1!==(o=e.indexOf("#"))&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+i),e}},{"./../utils":49}],41:[function(e,t,r){"use strict";var s=e("./../utils");t.exports=s.isStandardBrowserEnv()?{write:function(e,t,r,n,o,i){var a=[];a.push(e+"="+encodeURIComponent(t)),s.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),s.isString(n)&&a.push("path="+n),s.isString(o)&&a.push("domain="+o),!0===i&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},{"./../utils":49}],46:[function(e,t,r){"use strict";var i=e("./../utils"),a=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(e){var t,r,n,o={};return e&&i.forEach(e.split("\n"),function(e){if(n=e.indexOf(":"),t=i.trim(e.substr(0,n)).toLowerCase(),r=i.trim(e.substr(n+1)),t){if(o[t]&&0<=a.indexOf(t))return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([r]):o[t]?o[t]+", "+r:r}}),o}},{"./../utils":49}],44:[function(e,t,r){"use strict";var n,o,i,a=e("./../utils");function s(e){var t=e;return o&&(i.setAttribute("href",t),t=i.href),i.setAttribute("href",t),{href:i.href,protocol:i.protocol?i.protocol.replace(/:$/,""):"",host:i.host,search:i.search?i.search.replace(/^\?/,""):"",hash:i.hash?i.hash.replace(/^#/,""):"",hostname:i.hostname,port:i.port,pathname:"/"===i.pathname.charAt(0)?i.pathname:"/"+i.pathname}}t.exports=a.isStandardBrowserEnv()?(o=/(msie|trident)/i.test(navigator.userAgent),i=document.createElement("a"),n=s(window.location.href),function(e){var t=a.isString(e)?s(e):e;return t.protocol===n.protocol&&t.host===n.host}):function(){return!0}},{"./../utils":49}],31:[function(e,t,r){"use strict";var a=e("./enhanceError");t.exports=function(e,t,r,n,o){var i=new Error(e);return a(i,t,r,n,o)}},{"./enhanceError":33}],35:[function(e,t,r){"use strict";var o=e("./createError");t.exports=function(e,t,r){var n=r.config.validateStatus;r.status&&n&&!n(r.status)?t(o("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},{"./createError":31}],30:[function(e,t,r){"use strict";var n=e("../helpers/isAbsoluteURL"),o=e("../helpers/combineURLs");t.exports=function(e,t){return e&&!n(t)?o(e,t):t}},{"../helpers/combineURLs":40,"../helpers/isAbsoluteURL":42}],38:[function(e,t,r){"use strict";t.exports=function(r,n){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];return r.apply(n,e)}}},{}],47:[function(e,t,r){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},{}],25:[function(e,t,r){"use strict";function n(e){this.message=e}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,t.exports=n},{}],27:[function(e,t,r){"use strict";t.exports=function(e){return!(!e||!e.__CANCEL__)}},{}],43:[function(e,t,r){"use strict";t.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},{}],34:[function(e,t,r){"use strict";var f=e("../utils");t.exports=function(t,r){r=r||{};var n={},e=["url","method","data"],o=["headers","auth","proxy","params"],i=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],a=["validateStatus"];function s(e,t){return f.isPlainObject(e)&&f.isPlainObject(t)?f.merge(e,t):f.isPlainObject(t)?f.merge({},t):f.isArray(t)?t.slice():t}function u(e){f.isUndefined(r[e])?f.isUndefined(t[e])||(n[e]=s(void 0,t[e])):n[e]=s(t[e],r[e])}f.forEach(e,function(e){f.isUndefined(r[e])||(n[e]=s(void 0,r[e]))}),f.forEach(o,u),f.forEach(i,function(e){f.isUndefined(r[e])?f.isUndefined(t[e])||(n[e]=s(void 0,t[e])):n[e]=s(void 0,r[e])}),f.forEach(a,function(e){e in r?n[e]=s(t[e],r[e]):e in t&&(n[e]=s(void 0,t[e]))});var l=e.concat(o).concat(i).concat(a),c=Object.keys(t).concat(Object.keys(r)).filter(function(e){return-1===l.indexOf(e)});return f.forEach(c,u),n}},{"../utils":49}],26:[function(e,t,r){"use strict";var n=e("./Cancel");function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var r=this;e(function(e){r.reason||(r.reason=new n(e),t(r.reason))})}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var t;return{token:new o(function(e){t=e}),cancel:t}},t.exports=o},{"./Cancel":25}],28:[function(e,t,r){"use strict";var n=e("./../utils"),o=e("../helpers/buildURL"),i=e("./InterceptorManager"),f=e("./dispatchRequest"),h=e("./mergeConfig"),p=e("../helpers/validator"),d=p.validators;function a(e){this.defaults=e,this.interceptors={request:new i,response:new i}}a.prototype.request=function(t,e){"string"==typeof t?(t=e||{}).url=arguments[0]:t=t||{},(t=h(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var r=t.transitional;void 0!==r&&p.assertOptions(r,{silentJSONParsing:d.transitional(d.boolean,"1.0.0"),forcedJSONParsing:d.transitional(d.boolean,"1.0.0"),clarifyTimeoutError:d.transitional(d.boolean,"1.0.0")},!1);var n=[],o=!0;this.interceptors.request.forEach(function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,n.unshift(e.fulfilled,e.rejected))});var i,a=[];if(this.interceptors.response.forEach(function(e){a.push(e.fulfilled,e.rejected)}),!o){var s=[f,void 0];for(Array.prototype.unshift.apply(s,n),s.concat(a),i=Promise.resolve(t);s.length;)i=i.then(s.shift(),s.shift());return i}for(var u=t;n.length;){var l=n.shift(),c=n.shift();try{u=l(u)}catch(e){c(e);break}}try{i=f(u)}catch(e){return Promise.reject(e)}for(;a.length;)i=i.then(a.shift(),a.shift());return i},a.prototype.getUri=function(e){return e=h(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],function(r){a.prototype[r]=function(e,t){return this.request(h(t||{},{method:r,url:e,data:(t||{}).data}))}}),n.forEach(["post","put","patch"],function(n){a.prototype[n]=function(e,t,r){return this.request(h(r||{},{method:n,url:e,data:t}))}}),t.exports=a},{"../helpers/buildURL":39,"../helpers/validator":48,"./../utils":49,"./InterceptorManager":29,"./dispatchRequest":32,"./mergeConfig":34}],37:[function(u,l,e){(function(s){(function(){"use strict";var i=u("./utils"),r=u("./helpers/normalizeHeaderName"),a=u("./core/enhanceError"),t={"Content-Type":"application/x-www-form-urlencoded"};function n(e,t){!i.isUndefined(e)&&i.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var e,o={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:("undefined"!=typeof XMLHttpRequest?e=u("./adapters/xhr"):void 0!==s&&"[object process]"===Object.prototype.toString.call(s)&&(e=u("./adapters/http")),e),transformRequest:[function(e,t){return r(t,"Accept"),r(t,"Content-Type"),i.isFormData(e)||i.isArrayBuffer(e)||i.isBuffer(e)||i.isStream(e)||i.isFile(e)||i.isBlob(e)?e:i.isArrayBufferView(e)?e.buffer:i.isURLSearchParams(e)?(n(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):i.isObject(e)||t&&"application/json"===t["Content-Type"]?(n(t,"application/json"),JSON.stringify(e)):e}],transformResponse:[function(e){var t=this.transitional,r=t&&t.silentJSONParsing,n=t&&t.forcedJSONParsing,o=!r&&"json"===this.responseType;if(o||n&&i.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(o){if("SyntaxError"===e.name)throw a(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return 200<=e&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};i.forEach(["delete","get","head"],function(e){o.headers[e]={}}),i.forEach(["post","put","patch"],function(e){o.headers[e]=i.merge(t)}),l.exports=o}).call(this)}).call(this,u("_process"))},{"./adapters/http":23,"./adapters/xhr":23,"./core/enhanceError":33,"./helpers/normalizeHeaderName":45,"./utils":49,_process:56}],48:[function(e,t,r){"use strict";var s=e("./../../package.json"),n={};["object","boolean","number","function","string","symbol"].forEach(function(t,r){n[t]=function(e){return typeof e===t||"a"+(r<1?"n ":" ")+t}});var u={},l=s.version.split(".");n.transitional=function(n,o,r){var i=o&&function(e,t){for(var r=t?t.split("."):l,n=e.split("."),o=0;o<3;o++){if(r[o]>n[o])return!0;if(r[o]<n[o])return!1}return!1}(o);function a(e,t){return"[Axios v"+s.version+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(e,t,r){if(!1===n)throw new Error(a(t," has been removed in "+o));return i&&!u[t]&&(u[t]=!0,console.warn(a(t," has been deprecated since v"+o+" and will be removed in the near future"))),!n||n(e,t,r)}},t.exports={assertOptions:function(e,t,r){if("object"!=typeof e)throw new TypeError("options must be an object");for(var n=Object.keys(e),o=n.length;0<o--;){var i=n[o],a=t[i];if(a){var s=e[i],u=void 0===s||a(s,i,e);if(!0!==u)throw new TypeError("option "+i+" must be "+u)}else if(!0!==r)throw Error("Unknown option "+i)}},validators:n}},{"./../../package.json":50}],29:[function(e,t,r){"use strict";var n=e("./../utils");function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(t){n.forEach(this.handlers,function(e){null!==e&&t(e)})},t.exports=o},{"./../utils":49}],32:[function(e,t,r){"use strict";var n=e("./../utils"),o=e("./transformData"),i=e("../cancel/isCancel"),a=e("../defaults");function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}t.exports=function(t){return s(t),t.headers=t.headers||{},t.data=o.call(t,t.data,t.headers,t.transformRequest),t.headers=n.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),n.forEach(["delete","get","head","post","put","patch","common"],function(e){delete t.headers[e]}),(t.adapter||a.adapter)(t).then(function(e){return s(t),e.data=o.call(t,e.data,e.headers,t.transformResponse),e},function(e){return i(e)||(s(t),e&&e.response&&(e.response.data=o.call(t,e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)})}},{"../cancel/isCancel":27,"../defaults":37,"./../utils":49,"./transformData":36}],42:[function(e,t,r){"use strict";t.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},{}],40:[function(e,t,r){"use strict";t.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},{}],33:[function(e,t,r){"use strict";t.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},{}],36:[function(e,t,r){"use strict";var o=e("./../utils"),i=e("./../defaults");t.exports=function(t,r,e){var n=this||i;return o.forEach(e,function(e){t=e.call(n,t,r)}),t}},{"./../defaults":37,"./../utils":49}],56:[function(e,t,r){var n,o,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(t){if(n===setTimeout)return setTimeout(t,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:a}catch(e){n=a}try{o="function"==typeof clearTimeout?clearTimeout:s}catch(e){o=s}}();var l,c=[],f=!1,h=-1;function p(){f&&l&&(f=!1,l.length?c=l.concat(c):h=-1,c.length&&d())}function d(){if(!f){var e=u(p);f=!0;for(var t=c.length;t;){for(l=c,c=[];++h<t;)l&&l[h].run();h=-1,t=c.length}l=null,f=!1,function(t){if(o===clearTimeout)return clearTimeout(t);if((o===s||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(e)}}function b(e,t){this.fun=e,this.array=t}function y(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new b(e,t)),1!==c.length||f||u(d)},b.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=y,i.addListener=y,i.once=y,i.off=y,i.removeListener=y,i.removeAllListeners=y,i.emit=y,i.prependListener=y,i.prependOnceListener=y,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],45:[function(e,t,r){"use strict";var o=e("../utils");t.exports=function(r,n){o.forEach(r,function(e,t){t!==n&&t.toUpperCase()===n.toUpperCase()&&(r[n]=e,delete r[t])})}},{"../utils":49}],50:[function(e,t,r){t.exports={version:"0.21.2"}},{}],51:[function(e,t,r){"use strict";r.toByteArray=function(e){var t,r,n=f(e),o=n[0],i=n[1],a=new c(function(e,t){return 3*(e+t)/4-t}(o,i)),s=0,u=0<i?o-4:o;for(r=0;r<u;r+=4)t=l[e.charCodeAt(r)]<<18|l[e.charCodeAt(r+1)]<<12|l[e.charCodeAt(r+2)]<<6|l[e.charCodeAt(r+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;2===i&&(t=l[e.charCodeAt(r)]<<2|l[e.charCodeAt(r+1)]>>4,a[s++]=255&t);1===i&&(t=l[e.charCodeAt(r)]<<10|l[e.charCodeAt(r+1)]<<4|l[e.charCodeAt(r+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t);return a},r.fromByteArray=function(e){for(var t,r=e.length,n=r%3,o=[],i=0,a=r-n;i<a;i+=16383)o.push(function(e,t,r){for(var n,o=[],i=t;i<r;i+=3)n=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2]),o.push(function(e){return s[e>>18&63]+s[e>>12&63]+s[e>>6&63]+s[63&e]}(n));return o.join("")}(e,i,a<i+16383?a:i+16383));1==n?(t=e[r-1],o.push(s[t>>2]+s[t<<4&63]+"==")):2==n&&(t=(e[r-2]<<8)+e[r-1],o.push(s[t>>10]+s[t>>4&63]+s[t<<2&63]+"="));return o.join("")};for(var s=[],l=[],c="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,i=n.length;o<i;++o)s[o]=n[o],l[n.charCodeAt(o)]=o;function f(e){var t=e.length;if(0<t%4)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},{}],52:[function(e,t,r){},{}],53:[function(A,e,j){(function(e){(function(){"use strict";var s=A("base64-js"),i=A("ieee754");j.Buffer=f,j.INSPECT_MAX_BYTES=50;var r=2147483647;function a(e){if(r<e)throw new RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return t.__proto__=f.prototype,t}function f(e,t,r){if("number"!=typeof e)return n(e,t,r);if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return o(e)}function n(e,t,r){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!f.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var r=0|h(e,t),n=a(r),o=n.write(e,t);o!==r&&(n=n.slice(0,o));return n}(e,t);if(ArrayBuffer.isView(e))return l(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(S(e,ArrayBuffer)||e&&S(e.buffer,ArrayBuffer))return function(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r);return n.__proto__=f.prototype,n}(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return f.from(n,t,r);var o=function(e){if(f.isBuffer(e)){var t=0|c(e.length),r=a(t);return 0===r.length?r:(e.copy(r,0,0,t),r)}if(void 0!==e.length)return"number"!=typeof e.length||P(e.length)?a(0):l(e);if("Buffer"===e.type&&Array.isArray(e.data))return l(e.data)}(e);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return f.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function o(e){return u(e),a(e<0?0:0|c(e))}function l(e){for(var t=e.length<0?0:0|c(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function c(e){if(r<=e)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r.toString(16)+" bytes");return 0|e}function h(e,t){if(f.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||S(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=2<arguments.length&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return _(e).length;default:if(o)return n?-1:O(e).length;t=(""+t).toLowerCase(),o=!0}}function t(e,t,r){var n,o,i,a=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e=e||"utf8";;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0);(!r||r<0||n<r)&&(r=n);for(var o="",i=t;i<r;++i)o+=function(e){return e<16?"0"+e.toString(16):e.toString(16)}(e[i]);return o}(this,t,r);case"utf8":case"utf-8":return v(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(127&e[o]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(e[o]);return n}(this,t,r);case"base64":return n=this,i=r,0===(o=t)&&i===n.length?s.fromByteArray(n):s.fromByteArray(n.slice(o,i));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}(this,t,r);default:if(a)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),a=!0}}function p(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function d(e,t,r,n,o){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):2147483647<r?r=2147483647:r<-2147483648&&(r=-2147483648),P(r=+r)&&(r=o?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(o)return-1;r=e.length-1}else if(r<0){if(!o)return-1;r=0}if("string"==typeof t&&(t=f.from(t,n)),f.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,o);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,o);throw new TypeError("val must be string, number or Buffer")}function b(e,t,r,n,o){var i=1,a=e.length,s=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a/=i=2,s/=2,r/=2}function u(e,t){return 1===i?e[t]:e.readUInt16BE(t*i)}if(o)for(var l=-1,c=r;c<a;c++)if(u(e,c)===u(t,-1===l?0:c-l)){if(-1===l&&(l=c),c-l+1===s)return l*i}else-1!==l&&(c-=c-l),l=-1;else for(a<r+s&&(r=a-s),c=r;0<=c;c--){for(var f=!0,h=0;h<s;h++)if(u(e,c+h)!==u(t,h)){f=!1;break}if(f)return c}return-1}function y(e,t,r,n){return T(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function m(e,t,r,n){return T(function(e,t){for(var r,n,o,i=[],a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,o=r%256,i.push(o),i.push(n);return i}(t,e.length-r),e,r,n)}function v(e,t,r){r=Math.min(e.length,r);for(var n=[],o=t;o<r;){var i,a,s,u,l=e[o],c=null,f=239<l?4:223<l?3:191<l?2:1;if(o+f<=r)switch(f){case 1:l<128&&(c=l);break;case 2:128==(192&(i=e[o+1]))&&127<(u=(31&l)<<6|63&i)&&(c=u);break;case 3:i=e[o+1],a=e[o+2],128==(192&i)&&128==(192&a)&&2047<(u=(15&l)<<12|(63&i)<<6|63&a)&&(u<55296||57343<u)&&(c=u);break;case 4:i=e[o+1],a=e[o+2],s=e[o+3],128==(192&i)&&128==(192&a)&&128==(192&s)&&65535<(u=(15&l)<<18|(63&i)<<12|(63&a)<<6|63&s)&&u<1114112&&(c=u)}null===c?(c=65533,f=1):65535<c&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),o+=f}return function(e){var t=e.length;if(t<=g)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=g));return r}(n)}(f.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}())||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),f.poolSize=8192,f.from=n,f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,f.alloc=function(e,t,r){return o=t,i=r,u(n=e),n<=0||void 0===o?a(n):"string"==typeof i?a(n).fill(o,i):a(n).fill(o);var n,o,i},f.allocUnsafe=o,f.allocUnsafeSlow=o,f.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==f.prototype},f.compare=function(e,t){if(S(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),S(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,o=0,i=Math.min(r,n);o<i;++o)if(e[o]!==t[o]){r=e[o],n=t[o];break}return r<n?-1:n<r?1:0},f.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return f.alloc(0);if(void 0===t)for(o=t=0;o<e.length;++o)t+=e[o].length;for(var r=f.allocUnsafe(t),n=0,o=0;o<e.length;++o){var i=e[o];if(S(i,Uint8Array)&&(i=f.from(i)),!f.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(r,n),n+=i.length}return r},f.byteLength=h,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)p(this,t,t+1);return this},f.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)p(this,t,t+3),p(this,t+1,t+2);return this},f.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)p(this,t,t+7),p(this,t+1,t+6),p(this,t+2,t+5),p(this,t+3,t+4);return this},f.prototype.toLocaleString=f.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?v(this,0,e):t.apply(this,arguments)},f.prototype.equals=function(e){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===f.compare(this,e)},f.prototype.inspect=function(){var e="",t=j.INSPECT_MAX_BYTES,e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim();return this.length>t&&(e+=" ... "),"<Buffer "+e+">"},f.prototype.compare=function(e,t,r,n,o){if(S(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),t<0||r>e.length||n<0||o>this.length)throw new RangeError("out of range index");if(o<=n&&r<=t)return 0;if(o<=n)return-1;if(r<=t)return 1;if(this===e)return 0;for(var i=(o>>>=0)-(n>>>=0),a=(r>>>=0)-(t>>>=0),s=Math.min(i,a),u=this.slice(n,o),l=e.slice(t,r),c=0;c<s;++c)if(u[c]!==l[c]){i=u[c],a=l[c];break}return i<a?-1:a<i?1:0},f.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},f.prototype.indexOf=function(e,t,r){return d(this,e,t,r,!0)},f.prototype.lastIndexOf=function(e,t,r){return d(this,e,t,r,!1)},f.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var o=this.length-t;if((void 0===r||o<r)&&(r=o),0<e.length&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n=n||"utf8";for(var i,a,s,u,l,c,f=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var o=e.length-r;(!n||o<(n=Number(n)))&&(n=o);var i=t.length;i/2<n&&(n=i/2);for(var a=0;a<n;++a){var s=parseInt(t.substr(2*a,2),16);if(P(s))return a;e[r+a]=s}return a}(this,e,t,r);case"utf8":case"utf-8":return l=t,c=r,T(O(e,(u=this).length-l),u,l,c);case"ascii":return y(this,e,t,r);case"latin1":case"binary":return y(this,e,t,r);case"base64":return i=this,a=t,s=r,T(_(e),i,a,s);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return m(this,e,t,r);default:if(f)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var g=4096;function w(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(r<e+t)throw new RangeError("Trying to access beyond buffer length")}function C(e,t,r,n,o,i){if(!f.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(o<t||t<i)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function x(e,t,r,n){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function R(e,t,r,n,o){return t=+t,r>>>=0,o||x(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function E(e,t,r,n,o){return t=+t,r>>>=0,o||x(e,0,r,8),i.write(e,t,r,n,52,8),r+8}f.prototype.slice=function(e,t){var r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):r<e&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):r<t&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return n.__proto__=f.prototype,n},f.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n},f.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e+--t],o=1;0<t&&(o*=256);)n+=this[e+--t]*o;return n},f.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},f.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},f.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},f.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},f.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},f.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return(o*=128)<=n&&(n-=Math.pow(2,8*t)),n},f.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=t,o=1,i=this[e+--n];0<n&&(o*=256);)i+=this[e+--n]*o;return(o*=128)<=i&&(i-=Math.pow(2,8*t)),i},f.prototype.readInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},f.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},f.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},f.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!0,23,4)},f.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!1,23,4)},f.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!0,52,8)},f.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!1,52,8)},f.prototype.writeUIntLE=function(e,t,r,n){e=+e,t>>>=0,r>>>=0,n||C(this,e,t,r,Math.pow(2,8*r)-1,0);var o=1,i=0;for(this[t]=255&e;++i<r&&(o*=256);)this[t+i]=e/o&255;return t+r},f.prototype.writeUIntBE=function(e,t,r,n){e=+e,t>>>=0,r>>>=0,n||C(this,e,t,r,Math.pow(2,8*r)-1,0);var o=r-1,i=1;for(this[t+o]=255&e;0<=--o&&(i*=256);)this[t+o]=e/i&255;return t+r},f.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,1,255,0),this[t]=255&e,t+1},f.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},f.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},f.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},f.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},f.prototype.writeIntLE=function(e,t,r,n){var o;e=+e,t>>>=0,n||C(this,e,t,r,(o=Math.pow(2,8*r-1))-1,-o);var i=0,a=1,s=0;for(this[t]=255&e;++i<r&&(a*=256);)e<0&&0===s&&0!==this[t+i-1]&&(s=1),this[t+i]=(e/a>>0)-s&255;return t+r},f.prototype.writeIntBE=function(e,t,r,n){var o;e=+e,t>>>=0,n||C(this,e,t,r,(o=Math.pow(2,8*r-1))-1,-o);var i=r-1,a=1,s=0;for(this[t+i]=255&e;0<=--i&&(a*=256);)e<0&&0===s&&0!==this[t+i+1]&&(s=1),this[t+i]=(e/a>>0)-s&255;return t+r},f.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},f.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},f.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},f.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},f.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},f.prototype.writeFloatLE=function(e,t,r){return R(this,e,t,!0,r)},f.prototype.writeFloatBE=function(e,t,r){return R(this,e,t,!1,r)},f.prototype.writeDoubleLE=function(e,t,r){return E(this,e,t,!0,r)},f.prototype.writeDoubleBE=function(e,t,r){return E(this,e,t,!1,r)},f.prototype.copy=function(e,t,r,n){if(!f.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r=r||0,n||0===n||(n=this.length),t>=e.length&&(t=e.length),t=t||0,0<n&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var o=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var i=o-1;0<=i;--i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return o},f.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n);var o;1===e.length&&(o=e.charCodeAt(0),("utf8"===n&&o<128||"latin1"===n)&&(e=o))}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var i;if(t>>>=0,r=void 0===r?this.length:r>>>0,"number"==typeof(e=e||0))for(i=t;i<r;++i)this[i]=e;else{var a=f.isBuffer(e)?e:f.from(e,n),s=a.length;if(0===s)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=a[i%s]}return this};var k=/[^+/0-9A-Za-z-_]/g;function O(e,t){var r;t=t||1/0;for(var n=e.length,o=null,i=[],a=0;a<n;++a){if(55295<(r=e.charCodeAt(a))&&r<57344){if(!o){if(56319<r){-1<(t-=3)&&i.push(239,191,189);continue}if(a+1===n){-1<(t-=3)&&i.push(239,191,189);continue}o=r;continue}if(r<56320){-1<(t-=3)&&i.push(239,191,189),o=r;continue}r=65536+(o-55296<<10|r-56320)}else o&&-1<(t-=3)&&i.push(239,191,189);if(o=null,r<128){if(--t<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function _(e){return s.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(k,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function T(e,t,r,n){for(var o=0;o<n&&!(o+r>=t.length||o>=e.length);++o)t[o+r]=e[o];return o}function S(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function P(e){return e!=e}}).call(this)}).call(this,A("buffer").Buffer)},{"base64-js":51,buffer:53,ieee754:54}],54:[function(e,t,r){r.read=function(e,t,r,n,o){var i,a,s=8*o-n-1,u=(1<<s)-1,l=u>>1,c=-7,f=r?o-1:0,h=r?-1:1,p=e[t+f];for(f+=h,i=p&(1<<-c)-1,p>>=-c,c+=s;0<c;i=256*i+e[t+f],f+=h,c-=8);for(a=i&(1<<-c)-1,i>>=-c,c+=n;0<c;a=256*a+e[t+f],f+=h,c-=8);if(0===i)i=1-l;else{if(i===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,n),i-=l}return(p?-1:1)*a*Math.pow(2,i-n)},r.write=function(e,t,r,n,o,i){var a,s,u,l=8*i-o-1,c=(1<<l)-1,f=c>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:i-1,d=n?1:-1,b=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),2<=(t+=1<=a+f?h/u:h*Math.pow(2,1-f))*u&&(a++,u/=2),c<=a+f?(s=0,a=c):1<=a+f?(s=(t*u-1)*Math.pow(2,o),a+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,o),a=0));8<=o;e[r+p]=255&s,p+=d,s/=256,o-=8);for(a=a<<o|s,l+=o;0<l;e[r+p]=255&a,p+=d,a/=256,l-=8);e[r+p-d]|=128*b}},{}],55:[function(e,r,t){!function(e,t){"use strict";"object"==typeof r&&r.exports?r.exports=t():e.log=t()}(this,function(){"use strict";var o=function(){},u="undefined",i=typeof window!==u&&typeof window.navigator!==u&&/Trident\/|MSIE /.test(window.navigator.userAgent),l=["trace","debug","info","warn","error"];function a(t,e){var r=t[e];if("function"==typeof r.bind)return r.bind(t);try{return Function.prototype.bind.call(r,t)}catch(e){return function(){return Function.prototype.apply.apply(r,[t,arguments])}}}function s(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(e,t){for(var r=0;r<l.length;r++){var n=l[r];this[n]=r<e?o:this.methodFactory(n,e,t)}this.log=this.debug}function f(e,t,r){return"debug"===(n=e)&&(n="log"),typeof console!==u&&("trace"===n&&i?s:void 0!==console[n]?a(console,n):void 0!==console.log?a(console,"log"):o)||function(e,t,r){return function(){typeof console!==u&&(c.call(this,t,r),this[e].apply(this,arguments))}}.apply(this,arguments);var n}function t(r,e,t){var n,o=this,i="loglevel";function a(){var e;if(typeof window!==u&&i){try{e=window.localStorage[i]}catch(e){}if(typeof e===u)try{var t=window.document.cookie,r=t.indexOf(encodeURIComponent(i)+"=");-1!==r&&(e=/^([^;]+)/.exec(t.slice(r))[1])}catch(e){}return void 0===o.levels[e]&&(e=void 0),e}}"string"==typeof r?i+=":"+r:"symbol"==typeof r&&(i=void 0),o.name=r,o.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},o.methodFactory=t||f,o.getLevel=function(){return n},o.setLevel=function(e,t){if("string"==typeof e&&void 0!==o.levels[e.toUpperCase()]&&(e=o.levels[e.toUpperCase()]),!("number"==typeof e&&0<=e&&e<=o.levels.SILENT))throw"log.setLevel() called with invalid level: "+e;if(n=e,!1!==t&&function(e){var t=(l[e]||"silent").toUpperCase();if(typeof window!==u&&i){try{return window.localStorage[i]=t}catch(e){}try{window.document.cookie=encodeURIComponent(i)+"="+t+";"}catch(e){}}}(e),c.call(o,e,r),typeof console===u&&e<o.levels.SILENT)return"No console available for logging"},o.setDefaultLevel=function(e){a()||o.setLevel(e,!1)},o.enableAll=function(e){o.setLevel(o.levels.TRACE,e)},o.disableAll=function(e){o.setLevel(o.levels.SILENT,e)};var s=a();null==s&&(s=null==e?"WARN":e),o.setLevel(s,!1)}var r=new t,n={};r.getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");return n[e]||(n[e]=new t(e,r.getLevel(),r.methodFactory))};var e=typeof window!==u?window.log:void 0;return r.noConflict=function(){return typeof window!==u&&window.log===r&&(window.log=e),r},r.getLoggers=function(){return n},r.default=r})},{}],58:[function(e,t,r){"use strict";var n=Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]},o=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t},a=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0}),r.Errors=r.SearchClient=r.Client=void 0;var s=a(e("./Typesense/Client"));r.Client=s.default;var u=a(e("./Typesense/SearchClient"));r.SearchClient=u.default;var l=i(e("./Typesense/Errors"));r.Errors=l,r.default={Client:s.default,SearchClient:u.default,Errors:l}},{"./Typesense/Client":62,"./Typesense/Errors":79,"./Typesense/SearchClient":89}],89:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass")),a=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var s=a(e("./Configuration")),u=a(e("./ApiCall")),l=a(e("./MultiSearch")),c=e("./SearchOnlyCollection"),f=function(){function t(e){(0,o.default)(this,t),(e.apiKey||"").length<2e3&&(e.sendApiKeyAsQueryParam=!0),this.configuration=new s.default(e),this.apiCall=new u.default(this.configuration),this.multiSearch=new l.default(this.apiCall,this.configuration,!0),this.individualCollections={}}return(0,i.default)(t,[{key:"collections",value:function(e){if(e)return void 0===this.individualCollections[e]&&(this.individualCollections[e]=new c.SearchOnlyCollection(e,this.apiCall,this.configuration)),this.individualCollections[e];throw new Error("Typesense.SearchClient only supports search operations, so the collectionName that needs to be searched must be specified. Use Typesense.Client if you need to access the collection object.")}}]),t}();r.default=f},{"./ApiCall":61,"./Configuration":65,"./MultiSearch":84,"./SearchOnlyCollection":90,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}],79:[function(e,t,r){"use strict";var n=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0}),r.ImportError=r.TypesenseError=r.ServerError=r.RequestUnauthorized=r.RequestMalformed=r.ObjectUnprocessable=r.ObjectNotFound=r.ObjectAlreadyExists=r.MissingConfigurationError=r.HTTPError=void 0;var o=n(e("./HTTPError"));r.HTTPError=o.default;var i=n(e("./MissingConfigurationError"));r.MissingConfigurationError=i.default;var a=n(e("./ObjectAlreadyExists"));r.ObjectAlreadyExists=a.default;var s=n(e("./ObjectNotFound"));r.ObjectNotFound=s.default;var u=n(e("./ObjectUnprocessable"));r.ObjectUnprocessable=u.default;var l=n(e("./RequestMalformed"));r.RequestMalformed=l.default;var c=n(e("./RequestUnauthorized"));r.RequestUnauthorized=c.default;var f=n(e("./ServerError"));r.ServerError=f.default;var h=n(e("./ImportError"));r.ImportError=h.default;var p=n(e("./TypesenseError"));r.TypesenseError=p.default},{"./HTTPError":69,"./ImportError":70,"./MissingConfigurationError":71,"./ObjectAlreadyExists":72,"./ObjectNotFound":73,"./ObjectUnprocessable":74,"./RequestMalformed":75,"./RequestUnauthorized":76,"./ServerError":77,"./TypesenseError":78}],62:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass")),a=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var s=a(e("./Configuration")),u=a(e("./ApiCall")),l=a(e("./Collections")),c=a(e("./Collection")),f=a(e("./Aliases")),h=a(e("./Alias")),p=a(e("./Keys")),d=a(e("./Key")),b=a(e("./Debug")),y=a(e("./Metrics")),m=a(e("./Health")),v=a(e("./Operations")),g=a(e("./MultiSearch")),w=function(){function t(e){(0,o.default)(this,t),this.configuration=new s.default(e),this.apiCall=new u.default(this.configuration),this.debug=new b.default(this.apiCall),this.metrics=new y.default(this.apiCall),this.health=new m.default(this.apiCall),this.operations=new v.default(this.apiCall),this.multiSearch=new g.default(this.apiCall,this.configuration),this._collections=new l.default(this.apiCall),this.individualCollections={},this._aliases=new f.default(this.apiCall),this.individualAliases={},this._keys=new p.default(this.apiCall),this.individualKeys={}}return(0,i.default)(t,[{key:"collections",value:function(e){return void 0===e?this._collections:(void 0===this.individualCollections[e]&&(this.individualCollections[e]=new c.default(e,this.apiCall,this.configuration)),this.individualCollections[e])}},{key:"aliases",value:function(e){return void 0===e?this._aliases:(void 0===this.individualAliases[e]&&(this.individualAliases[e]=new h.default(e,this.apiCall)),this.individualAliases[e])}},{key:"keys",value:function(e){return void 0===e?this._keys:(void 0===this.individualKeys[e]&&(this.individualKeys[e]=new d.default(e,this.apiCall)),this.individualKeys[e])}}]),t}();r.default=w},{"./Alias":59,"./Aliases":60,"./ApiCall":61,"./Collection":63,"./Collections":64,"./Configuration":65,"./Debug":66,"./Health":80,"./Key":81,"./Keys":82,"./Metrics":83,"./MultiSearch":84,"./Operations":85,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}],59:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Aliases")),c=function(){function r(e,t){(0,a.default)(this,r),this.name=e,this.apiCall=t}var e,t;return(0,s.default)(r,[{key:"retrieve",value:(t=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.name)}}]),r}();r.default=c},{"./Aliases":60,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],60:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var u="/aliases",l=function(){function t(e){(0,a.default)(this,t),this.apiCall=e}var e,r;return(0,s.default)(t,[{key:"upsert",value:(r=(0,i.default)(o.default.mark(function e(t,r){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.put(this.endpointPath(t),r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e,t){return r.apply(this,arguments)})},{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(u);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(e){return"".concat(t.RESOURCEPATH,"/").concat(e)}}],[{key:"RESOURCEPATH",get:function(){return u}}]),t}();r.default=l},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],61:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),E=n(e("@babel/runtime/regenerator")),k=n(e("@babel/runtime/helpers/typeof")),o=n(e("@babel/runtime/helpers/asyncToGenerator")),i=n(e("@babel/runtime/helpers/classCallCheck")),a=n(e("@babel/runtime/helpers/createClass")),s=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var O=s(e("axios")),u=e("./Errors"),l=s(e("./Errors/TypesenseError")),c=function(){function t(e){(0,i.default)(this,t),this.configuration=e,this.apiKey=this.configuration.apiKey,this.nodes=JSON.parse(JSON.stringify(this.configuration.nodes)),this.nearestNode=JSON.parse(JSON.stringify(this.configuration.nearestNode)),this.connectionTimeoutSeconds=this.configuration.connectionTimeoutSeconds,this.healthcheckIntervalSeconds=this.configuration.healthcheckIntervalSeconds,this.numRetriesPerRequest=this.configuration.numRetries,this.retryIntervalSeconds=this.configuration.retryIntervalSeconds,this.sendApiKeyAsQueryParam=this.configuration.sendApiKeyAsQueryParam,this.additionalUserHeaders=this.configuration.additionalHeaders,this.logger=this.configuration.logger,this.initializeMetadataForNodes(),this.currentNodeIndex=-1}var r,n;return(0,a.default)(t,[{key:"get",value:function(e,t,r){var n=1<arguments.length&&void 0!==t?t:{},o=2<arguments.length&&void 0!==r?r:{},i=o.abortSignal,a=void 0===i?null:i,s=o.responseType,u=void 0===s?null:s;return this.performRequest("get",e,{queryParameters:n,abortSignal:a,responseType:u})}},{key:"delete",value:function(e,t){var r=1<arguments.length&&void 0!==t?t:{};return this.performRequest("delete",e,{queryParameters:r})}},{key:"post",value:function(e,t,r,n){var o=1<arguments.length&&void 0!==t?t:{},i=2<arguments.length&&void 0!==r?r:{},a=3<arguments.length&&void 0!==n?n:{};return this.performRequest("post",e,{queryParameters:i,bodyParameters:o,additionalHeaders:a})}},{key:"put",value:function(e,t,r){var n=1<arguments.length&&void 0!==t?t:{},o=2<arguments.length&&void 0!==r?r:{};return this.performRequest("put",e,{queryParameters:o,bodyParameters:n})}},{key:"patch",value:function(e,t,r){var n=1<arguments.length&&void 0!==t?t:{},o=2<arguments.length&&void 0!==r?r:{};return this.performRequest("patch",e,{queryParameters:o,bodyParameters:n})}},{key:"performRequest",value:(n=(0,o.default)(E.default.mark(function e(t,r,n){var o,i,a,s,u,l,c,f,h,p,d,b,y,m,v,g,w,C,x,R;return E.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:o=n.queryParameters,i=void 0===o?null:o,a=n.bodyParameters,s=void 0===a?null:a,u=n.additionalHeaders,l=void 0===u?{}:u,c=n.abortSignal,f=void 0===c?null:c,h=n.responseType,p=void 0===h?null:h,this.configuration.validate(),m=Date.now(),this.logger.debug("Request #".concat(m,": Performing ").concat(t.toUpperCase()," request: ").concat(r)),g=1;case 5:if(!(g<=this.numRetriesPerRequest+1)){e.next=47;break}if(w=this.getNextNode(m),this.logger.debug("Request #".concat(m,": Attempting ").concat(t.toUpperCase()," request Try #").concat(g," to Node ").concat(w.index)),f&&f.aborted)return e.abrupt("return",Promise.reject(new Error("Request aborted by caller.")));e.next=10;break;case 10:return C=void 0,e.prev=11,x={method:t,url:this.uriFor(r,w),headers:Object.assign({},this.defaultHeaders(),l,this.additionalUserHeaders),timeout:1e3*this.connectionTimeoutSeconds,maxContentLength:1/0,maxBodyLength:1/0,responseType:p,validateStatus:function(e){return 0<e},transformResponse:[function(e,t){var r=e;return void 0!==t&&"string"==typeof e&&t["content-type"]&&t["content-type"].startsWith("application/json")&&(r=JSON.parse(e)),r}]},i&&0!==Object.keys(i).length&&(x.params=i),this.sendApiKeyAsQueryParam&&(x.params=x.params||{},x.params["x-typesense-api-key"]=this.apiKey),s&&("string"==typeof s&&0!==s.length||"object"===(0,k.default)(s)&&0!==Object.keys(s).length)&&(x.data=s),f&&function(){var e=O.default.CancelToken.source();C=function(){return e.cancel()},f.addEventListener("abort",C),x.cancelToken=e.token}(),e.next=19,O.default(x);case 19:if(1<=(R=e.sent).status&&R.status<=499&&this.setNodeHealthcheck(w,!0),this.logger.debug("Request #".concat(m,": Request to Node ").concat(w.index," was made. Response Code was ").concat(R.status,".")),200<=R.status&&R.status<300)return e.abrupt("return",Promise.resolve(R.data));e.next=26;break;case 26:if(R.status<500)return e.abrupt("return",Promise.reject(this.customErrorForResponse(R,null===(d=R.data)||void 0===d?void 0:d.message)));e.next=30;break;case 30:throw this.customErrorForResponse(R,null===(b=R.data)||void 0===b?void 0:b.message);case 31:e.next=41;break;case 33:return e.prev=33,e.t0=e.catch(11),this.setNodeHealthcheck(w,!1),v=e.t0,this.logger.warn("Request #".concat(m,": Request to Node ").concat(w.index,' failed due to "').concat(e.t0.code," ").concat(e.t0.message).concat(null==e.t0.response?"":" - "+JSON.stringify(null===(y=e.t0.response)||void 0===y?void 0:y.data),'"')),this.logger.warn("Request #".concat(m,": Sleeping for ").concat(this.retryIntervalSeconds,"s and then retrying request...")),e.next=41,this.timer(this.retryIntervalSeconds);case 41:return e.prev=41,f&&C&&f.removeEventListener("abort",C),e.finish(41);case 44:g++,e.next=5;break;case 47:return this.logger.debug("Request #".concat(m,": No retries left. Raising last error")),e.abrupt("return",Promise.reject(v));case 49:case"end":return e.stop()}},e,this,[[11,33,41,44]])})),function(e,t,r){return n.apply(this,arguments)})},{key:"getNextNode",value:function(e){var t,r=0<arguments.length&&void 0!==e?e:0;if(null!=this.nearestNode){if(this.logger.debug("Request #".concat(r,": Nodes Health: Node ").concat(this.nearestNode.index," is ").concat(!0===this.nearestNode.isHealthy?"Healthy":"Unhealthy")),!0===this.nearestNode.isHealthy||this.nodeDueForHealthcheck(this.nearestNode,r))return this.logger.debug("Request #".concat(r,": Updated current node to Node ").concat(this.nearestNode.index)),this.nearestNode;this.logger.debug("Request #".concat(r,": Falling back to individual nodes"))}this.logger.debug("Request #".concat(r,": Nodes Health: ").concat(this.nodes.map(function(e){return"Node ".concat(e.index," is ").concat(!0===e.isHealthy?"Healthy":"Unhealthy")}).join(" || ")));for(var n=0;n<=this.nodes.length;n++)if(this.currentNodeIndex=(this.currentNodeIndex+1)%this.nodes.length,!0===(t=this.nodes[this.currentNodeIndex]).isHealthy||this.nodeDueForHealthcheck(t,r))return this.logger.debug("Request #".concat(r,": Updated current node to Node ").concat(t.index)),t;return this.logger.debug("Request #".concat(r,": No healthy nodes were found. Returning the next node, Node ").concat(t.index)),t}},{key:"nodeDueForHealthcheck",value:function(e,t){var r=1<arguments.length&&void 0!==t?t:0,n=Date.now()-e.lastAccessTimestamp>1e3*this.healthcheckIntervalSeconds;return n&&this.logger.debug("Request #".concat(r,": Node ").concat(e.index," has exceeded healtcheckIntervalSeconds of ").concat(this.healthcheckIntervalSeconds,". Adding it back into rotation.")),n}},{key:"initializeMetadataForNodes",value:function(){var r=this;null!=this.nearestNode&&(this.nearestNode.index="nearestNode",this.setNodeHealthcheck(this.nearestNode,!0)),this.nodes.forEach(function(e,t){e.index=t,r.setNodeHealthcheck(e,!0)})}},{key:"setNodeHealthcheck",value:function(e,t){e.isHealthy=t,e.lastAccessTimestamp=Date.now()}},{key:"uriFor",value:function(e,t){return null!=t.url?"".concat(t.url).concat(e):"".concat(t.protocol,"://").concat(t.host,":").concat(t.port).concat(t.path).concat(e)}},{key:"defaultHeaders",value:function(){var e={};return this.sendApiKeyAsQueryParam||(e["X-TYPESENSE-API-KEY"]=this.apiKey),e["Content-Type"]="application/json",e}},{key:"timer",value:(r=(0,o.default)(E.default.mark(function e(t){return E.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){return setTimeout(e,1e3*t)}));case 1:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)})},{key:"customErrorForResponse",value:function(e,t){var r="Request failed with HTTP code ".concat(e.status);"string"==typeof t&&""!==t.trim()&&(r+=" | Server said: ".concat(t));var n=new l.default(r);return(n=400===e.status?new u.RequestMalformed(r):401===e.status?new u.RequestUnauthorized(r):404===e.status?new u.ObjectNotFound(r):409===e.status?new u.ObjectAlreadyExists(r):422===e.status?new u.ObjectUnprocessable(r):500<=e.status&&e.status<=599?new u.ServerError(r):new u.HTTPError(r)).httpStatus=e.status,n}}]),t}();r.default=c},{"./Errors":79,"./Errors/TypesenseError":78,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/typeof":18,"@babel/runtime/regenerator":21,axios:22}],78:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf")),u=n(e("@babel/runtime/helpers/wrapNativeSuper"));function l(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=l(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}((0,u.default)(Error));r.default=c},{"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15,"@babel/runtime/helpers/wrapNativeSuper":20}],83:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function t(e){(0,o.default)(this,t),this.apiCall=e}return(0,i.default)(t,[{key:"retrieve",value:function(){return this.apiCall.get("/metrics.json")}}]),t}();r.default=a},{"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}],85:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var a=function(){function t(e){(0,o.default)(this,t),this.apiCall=e}return(0,i.default)(t,[{key:"perform",value:function(e,t){var r=1<arguments.length&&void 0!==t?t:{};return this.apiCall.post("".concat("/operations","/").concat(e),{},r)}}]),t}();r.default=a},{"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}],65:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass")),a=Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]},s=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&a(t,e,r);return s(t,e),t};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("loglevel")),c=e("./Errors"),f=function(){function r(e){var t=this;(0,o.default)(this,r),this.nodes=e.nodes||[],this.nodes=this.nodes.map(function(e){return t.setDefaultPathInNode(e)}).map(function(e){return t.setDefaultPortInNode(e)}),this.nearestNode=e.nearestNode||null,this.nearestNode=this.setDefaultPathInNode(this.nearestNode),this.nearestNode=this.setDefaultPortInNode(this.nearestNode),this.connectionTimeoutSeconds=e.connectionTimeoutSeconds||e.timeoutSeconds||10,this.healthcheckIntervalSeconds=e.healthcheckIntervalSeconds||15,this.numRetries=e.numRetries||this.nodes.length+(null==this.nearestNode?0:1)||3,this.retryIntervalSeconds=e.retryIntervalSeconds||.1,this.apiKey=e.apiKey,this.sendApiKeyAsQueryParam=e.sendApiKeyAsQueryParam||!1,this.cacheSearchResultsForSeconds=e.cacheSearchResultsForSeconds||0,this.useServerSideSearchCache=e.useServerSideSearchCache||!1,this.logger=e.logger||l,this.logLevel=e.logLevel||"warn",this.logger.setLevel(this.logLevel),this.additionalHeaders=e.additionalHeaders,this.showDeprecationWarnings(e),this.validate()}return(0,i.default)(r,[{key:"validate",value:function(){if(null==this.nodes||0===this.nodes.length||this.validateNodes())throw new c.MissingConfigurationError("Ensure that nodes[].protocol, nodes[].host and nodes[].port are set");if(null!=this.nearestNode&&this.isNodeMissingAnyParameters(this.nearestNode))throw new c.MissingConfigurationError("Ensure that nearestNodes.protocol, nearestNodes.host and nearestNodes.port are set");if(null==this.apiKey)throw new c.MissingConfigurationError("Ensure that apiKey is set");return!0}},{key:"validateNodes",value:function(){var t=this;return this.nodes.some(function(e){return t.isNodeMissingAnyParameters(e)})}},{key:"isNodeMissingAnyParameters",value:function(t){return!["protocol","host","port","path"].every(function(e){return t.hasOwnProperty(e)})&&null==t.url}},{key:"setDefaultPathInNode",value:function(e){return null==e||e.hasOwnProperty("path")||(e.path=""),e}},{key:"setDefaultPortInNode",value:function(e){if(null!=e&&!e.hasOwnProperty("port")&&e.hasOwnProperty("protocol"))switch(e.protocol){case"https":e.port=443;break;case"http":e.port=80}return e}},{key:"showDeprecationWarnings",value:function(e){e.timeoutSeconds&&this.logger.warn("Deprecation warning: timeoutSeconds is now renamed to connectionTimeoutSeconds"),e.masterNode&&this.logger.warn("Deprecation warning: masterNode is now consolidated to nodes, starting with Typesense Server v0.12"),e.readReplicaNodes&&this.logger.warn("Deprecation warning: readReplicaNodes is now consolidated to nodes, starting with Typesense Server v0.12")}}]),r}();r.default=f},{"./Errors":79,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,loglevel:55}],64:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var u="/collections",l=function(){function t(e){(0,a.default)(this,t),this.apiCall=e}var e,r;return(0,s.default)(t,[{key:"create",value:(r=(0,i.default)(o.default.mark(function e(t){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.post(u,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e){return r.apply(this,arguments)})},{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(u);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})}],[{key:"RESOURCEPATH",get:function(){return u}}]),t}();r.default=l},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],81:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Keys")),c=function(){function r(e,t){(0,a.default)(this,r),this.id=e,this.apiCall=t}var e,t;return(0,s.default)(r,[{key:"retrieve",value:(t=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.id)}}]),r}();r.default=c},{"./Keys":82,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],66:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var u=function(){function t(e){(0,a.default)(this,t),this.apiCall=e}var e;return(0,s.default)(t,[{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get("/debug");case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})}]),t}();r.default=u},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],80:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var u=function(){function t(e){(0,a.default)(this,t),this.apiCall=e}var e;return(0,s.default)(t,[{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get("/health");case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})}]),t}();r.default=u},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],82:[function(r,e,l){(function(u){(function(){"use strict";var e=r("@babel/runtime/helpers/interopRequireDefault"),n=e(r("@babel/runtime/regenerator")),o=e(r("@babel/runtime/helpers/asyncToGenerator")),i=e(r("@babel/runtime/helpers/classCallCheck")),a=e(r("@babel/runtime/helpers/createClass"));Object.defineProperty(l,"__esModule",{value:!0});var s=r("crypto"),t=function(){function r(e){(0,i.default)(this,r),this.apiCall=e,this.apiCall=e}var t;return(0,a.default)(r,[{key:"create",value:(t=(0,o.default)(n.default.mark(function e(t){return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.post(r.RESOURCEPATH,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"retrieve",value:function(){return this.apiCall.get("/keys")}},{key:"generateScopedSearchKey",value:function(e,t){var r=JSON.stringify(t),n=u.from(s.createHmac("sha256",e).update(r).digest("base64")),o=e.substr(0,4),i="".concat(n).concat(o).concat(r);return u.from(i).toString("base64")}}],[{key:"RESOURCEPATH",get:function(){return"/keys"}}]),r}();l.default=t}).call(this)}).call(this,r("buffer").Buffer)},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21,buffer:53,crypto:52}],63:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Collections")),c=u(e("./Documents")),f=e("./Errors"),h=u(e("./Overrides")),p=u(e("./Override")),d=u(e("./Synonyms")),b=u(e("./Synonym")),y=e("./Document"),m=function(){function n(e,t,r){(0,a.default)(this,n),this.name=e,this.apiCall=t,this.configuration=r,this.individualDocuments={},this.individualOverrides={},this.individualSynonyms={},this.name=e,this.apiCall=t,this.configuration=r,this._documents=new c.default(this.name,this.apiCall,this.configuration),this._overrides=new h.default(this.name,this.apiCall),this._synonyms=new d.default(this.name,this.apiCall)}var e,t,r;return(0,s.default)(n,[{key:"retrieve",value:(r=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"delete",value:(t=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"exists",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.retrieve();case 3:return e.abrupt("return",!0);case 6:if(e.prev=6,e.t0=e.catch(0),e.t0 instanceof f.ObjectNotFound)return e.abrupt("return",!1);e.next=10;break;case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(){return e.apply(this,arguments)})},{key:"documents",value:function(e){return e?(void 0===this.individualDocuments[e]&&(this.individualDocuments[e]=new y.Document(this.name,e,this.apiCall)),this.individualDocuments[e]):this._documents}},{key:"overrides",value:function(e){return void 0===e?this._overrides:(void 0===this.individualOverrides[e]&&(this.individualOverrides[e]=new p.default(this.name,e,this.apiCall)),this.individualOverrides[e])}},{key:"synonyms",value:function(e){return void 0===e?this._synonyms:(void 0===this.individualSynonyms[e]&&(this.individualSynonyms[e]=new b.default(this.name,e,this.apiCall)),this.individualSynonyms[e])}},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.name)}}]),n}();r.default=m},{"./Collections":64,"./Document":67,"./Documents":68,"./Errors":79,"./Override":86,"./Overrides":87,"./Synonym":92,"./Synonyms":93,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],84:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass")),a=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var s=a(e("./RequestWithCache")),u=function(){function n(e,t){var r=2<arguments.length&&void 0!==arguments[2]&&arguments[2];(0,o.default)(this,n),this.apiCall=e,this.configuration=t,this.useTextContentType=r,this.requestWithCache=new s.default}return(0,i.default)(n,[{key:"perform",value:function(e,t,r){var n=1<arguments.length&&void 0!==t?t:{},o=(2<arguments.length&&void 0!==r?r:{}).cacheSearchResultsForSeconds,i=void 0===o?this.configuration.cacheSearchResultsForSeconds:o,a={};this.useTextContentType&&(a["content-type"]="text/plain");var s={};!0===this.configuration.useServerSideSearchCache&&(s.usecache=!0);var u=Object.assign({},n,s);return this.requestWithCache.perform(this.apiCall,this.apiCall.post,["/multi_search",e,u,a],{cacheResponseForSeconds:i})}}]),n}();r.default=u},{"./RequestWithCache":88,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}],68:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),u=n(e("@babel/runtime/regenerator")),l=n(e("@babel/runtime/helpers/asyncToGenerator")),c=n(e("@babel/runtime/helpers/classCallCheck")),f=n(e("@babel/runtime/helpers/createClass")),h=n(e("@babel/runtime/helpers/inherits")),i=n(e("@babel/runtime/helpers/possibleConstructorReturn")),a=n(e("@babel/runtime/helpers/getPrototypeOf"));function p(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,a.default)(n);return t=o?(e=(0,a.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,i.default)(this,t)}}Object.defineProperty(r,"__esModule",{value:!0});var d=e("./Errors"),o=function(e){(0,h.default)(s,e);var t,r,n,o,i,a=p(s);function s(e,t,r){return(0,c.default)(this,s),a.call(this,e,t,r)}return(0,f.default)(s,[{key:"create",value:(i=(0,l.default)(u.default.mark(function e(t){var r,n=arguments;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<n.length&&void 0!==n[1]?n[1]:{},t){e.next=3;break}throw new Error("No document provided");case 3:return e.next=5,this.apiCall.post(this.endpointPath(),t,r);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"upsert",value:function(e,t){var r=1<arguments.length&&void 0!==t?t:{};if(!e)throw new Error("No document provided");return this.apiCall.post(this.endpointPath(),e,Object.assign({},r,{action:"upsert"}))}},{key:"update",value:function(e,t){var r=1<arguments.length&&void 0!==t?t:{};if(!e)throw new Error("No document provided");return this.apiCall.post(this.endpointPath(),e,Object.assign({},r,{action:"update"}))}},{key:"delete",value:function(e){var t=0<arguments.length&&void 0!==e?e:{};return"string"==typeof t?this.apiCall.delete(this.endpointPath(t),t):this.apiCall.delete(this.endpointPath(),t)}},{key:"createMany",value:(o=(0,l.default)(u.default.mark(function e(t){var r,n=arguments;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<n.length&&void 0!==n[1]?n[1]:{},this.configuration.logger.warn("createMany is deprecated and will be removed in a future version. Use import instead, which now takes both an array of documents or a JSONL string of documents"),e.abrupt("return",this.import(t,r));case 3:case"end":return e.stop()}},e,this)})),function(e){return o.apply(this,arguments)})},{key:"import",value:(n=(0,l.default)(u.default.mark(function e(t){var r,n,o,i,a,s=arguments;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<s.length&&void 0!==s[1]?s[1]:{},!Array.isArray(t)){e.next=13;break}e.prev=2,n=t.map(function(e){return JSON.stringify(e)}).join("\n"),e.next=11;break;case 6:if(e.prev=6,e.t0=e.catch(2),RangeError instanceof e.t0&&null!==e.t0&&void 0!==e.t0&&e.t0.includes("Too many properties to enumerate"))throw new Error("".concat(e.t0,"\n          It looks like you have reached a Node.js limit that restricts the number of keys in an Object: https://stackoverflow.com/questions/9282869/are-there-limits-to-the-number-of-properties-in-a-javascript-object\n\n          Please try reducing the number of keys in your document, or using CURL to import your data.\n          "));e.next=10;break;case 10:throw new Error(e.t0);case 11:e.next=14;break;case 13:n=t;case 14:return e.next=16,this.apiCall.performRequest("post",this.endpointPath("import"),{queryParameters:r,bodyParameters:n,additionalHeaders:{"Content-Type":"text/plain"}});case 16:if(o=e.sent,!Array.isArray(t)){e.next=27;break}if(i=o.split("\n").map(function(e){return JSON.parse(e)}),0<(a=i.filter(function(e){return!1===e.success})).length)throw new d.ImportError("".concat(i.length-a.length," documents imported successfully, ").concat(a.length," documents failed during import. Use `error.importResults` from the raised exception to get a detailed error reason for each document."),i);e.next=24;break;case 24:return e.abrupt("return",i);case 25:e.next=28;break;case 27:return e.abrupt("return",o);case 28:case"end":return e.stop()}},e,this,[[2,6]])})),function(e){return n.apply(this,arguments)})},{key:"export",value:(r=(0,l.default)(u.default.mark(function e(){var t,r=arguments;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0<r.length&&void 0!==r[0]?r[0]:{},e.abrupt("return",this.apiCall.get(this.endpointPath("export"),t));case 2:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"exportStream",value:(t=(0,l.default)(u.default.mark(function e(){var t,r=arguments;return u.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0<r.length&&void 0!==r[0]?r[0]:{},e.abrupt("return",this.apiCall.get(this.endpointPath("export"),t,{responseType:"stream"}));case 2:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})}]),s}(e("./SearchOnlyDocuments").SearchOnlyDocuments);r.default=o},{"./Errors":79,"./SearchOnlyDocuments":91,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15,"@babel/runtime/regenerator":21}],87:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Collections")),c=function(){function r(e,t){(0,a.default)(this,r),this.collectionName=e,this.apiCall=t}var e,n;return(0,s.default)(r,[{key:"upsert",value:(n=(0,i.default)(o.default.mark(function e(t,r){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.put(this.endpointPath(t),r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})},{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(e){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(r.RESOURCEPATH).concat(void 0===e?"":"/"+e)}}],[{key:"RESOURCEPATH",get:function(){return"/overrides"}}]),r}();r.default=c},{"./Collections":64,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],86:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Collections")),c=u(e("./Overrides")),f=function(){function n(e,t,r){(0,a.default)(this,n),this.collectionName=e,this.overrideId=t,this.apiCall=r}var e,t;return(0,s.default)(n,[{key:"retrieve",value:(t=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(c.default.RESOURCEPATH,"/").concat(this.overrideId)}}]),n}();r.default=f},{"./Collections":64,"./Overrides":87,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],93:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Collections")),c=function(){function r(e,t){(0,a.default)(this,r),this.collectionName=e,this.apiCall=t}var e,n;return(0,s.default)(r,[{key:"upsert",value:(n=(0,i.default)(o.default.mark(function e(t,r){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.put(this.endpointPath(t),r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})},{key:"retrieve",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(e){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(r.RESOURCEPATH).concat(void 0===e?"":"/"+e)}}],[{key:"RESOURCEPATH",get:function(){return"/synonyms"}}]),r}();r.default=c},{"./Collections":64,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],92:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var l=u(e("./Collections")),c=u(e("./Synonyms")),f=function(){function n(e,t,r){(0,a.default)(this,n),this.collectionName=e,this.synonymId=t,this.apiCall=r}var e,t;return(0,s.default)(n,[{key:"retrieve",value:(t=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(c.default.RESOURCEPATH,"/").concat(this.synonymId)}}]),n}();r.default=f},{"./Collections":64,"./Synonyms":93,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],67:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/regenerator")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass")),u=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0}),r.Document=void 0;var l=u(e("./Collections")),c=u(e("./Documents")),f=function(){function n(e,t,r){(0,a.default)(this,n),this.collectionName=e,this.documentId=t,this.apiCall=r}var t,e,r;return(0,s.default)(n,[{key:"retrieve",value:(r=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.get(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"delete",value:(e=(0,i.default)(o.default.mark(function e(){return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.apiCall.delete(this.endpointPath());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"update",value:(t=(0,i.default)(o.default.mark(function e(t){var r,n=arguments;return o.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<n.length&&void 0!==n[1]?n[1]:{},e.next=3,this.apiCall.patch(this.endpointPath(),t,r);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"endpointPath",value:function(){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(c.default.RESOURCEPATH,"/").concat(this.documentId)}}]),n}();r.Document=f},{"./Collections":64,"./Documents":68,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],91:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),c=n(e("@babel/runtime/regenerator")),o=n(e("@babel/runtime/helpers/asyncToGenerator")),i=n(e("@babel/runtime/helpers/classCallCheck")),a=n(e("@babel/runtime/helpers/createClass")),s=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0}),r.SearchOnlyDocuments=void 0;var u=s(e("./RequestWithCache")),l=s(e("./Collections")),f="/documents",h=function(){function n(e,t,r){(0,i.default)(this,n),this.collectionName=e,this.apiCall=t,this.configuration=r,this.requestWithCache=new u.default}var t;return(0,a.default)(n,[{key:"search",value:(t=(0,o.default)(c.default.mark(function e(t){var r,n,o,i,a,s,u,l=arguments;return c.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=(r=1<l.length&&void 0!==l[1]?l[1]:{}).cacheSearchResultsForSeconds,o=void 0===n?this.configuration.cacheSearchResultsForSeconds:n,i=r.abortSignal,a=void 0===i?null:i,s={},!0===this.configuration.useServerSideSearchCache&&(s.usecache=!0),u=Object.assign({},t,s),e.next=6,this.requestWithCache.perform(this.apiCall,this.apiCall.get,[this.endpointPath("search"),u,{abortSignal:a}],{cacheResponseForSeconds:o});case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"endpointPath",value:function(e){return"".concat(l.default.RESOURCEPATH,"/").concat(this.collectionName).concat(f).concat(void 0===e?"":"/"+e)}}],[{key:"RESOURCEPATH",get:function(){return f}}]),n}();r.SearchOnlyDocuments=h},{"./Collections":64,"./RequestWithCache":88,"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/regenerator":21}],69:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],70:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),i=n(e("@babel/runtime/helpers/classCallCheck")),a=n(e("@babel/runtime/helpers/inherits")),s=n(e("@babel/runtime/helpers/possibleConstructorReturn")),u=n(e("@babel/runtime/helpers/getPrototypeOf"));function l(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,u.default)(n);return t=o?(e=(0,u.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,s.default)(this,t)}}var o=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,a.default)(o,e);var n=l(o);function o(e,t){var r;return(0,i.default)(this,o),(r=n.call(this)).importResults=t,r}return o}(o(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],71:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],72:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],73:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],74:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],75:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],76:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],77:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/inherits")),a=n(e("@babel/runtime/helpers/possibleConstructorReturn")),s=n(e("@babel/runtime/helpers/getPrototypeOf"));function u(n){var o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t,r=(0,s.default)(n);return t=o?(e=(0,s.default)(this).constructor,Reflect.construct(r,arguments,e)):r.apply(this,arguments),(0,a.default)(this,t)}}var l=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,"__esModule",{value:!0});var c=function(e){(0,i.default)(r,e);var t=u(r);function r(){return(0,o.default)(this,r),t.apply(this,arguments)}return r}(l(e("./TypesenseError")).default);r.default=c},{"./TypesenseError":78,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/getPrototypeOf":8,"@babel/runtime/helpers/inherits":9,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/possibleConstructorReturn":15}],88:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),d=n(e("@babel/runtime/regenerator")),b=n(e("@babel/runtime/helpers/toConsumableArray")),i=n(e("@babel/runtime/helpers/asyncToGenerator")),a=n(e("@babel/runtime/helpers/classCallCheck")),s=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0});var o=function(){function e(){(0,a.default)(this,e),this.responseCache=new Map}var o;return(0,s.default)(e,[{key:"perform",value:(o=(0,i.default)(d.default.mark(function e(t,r,n,o){var i,a,s,u,l,c,f,h,p;return d.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=o.cacheResponseForSeconds,a=void 0===i?120:i,s=o.maxSize,u=void 0===s?100:s,a<=0||u<=0)return e.abrupt("return",r.call.apply(r,[t].concat((0,b.default)(n))));e.next=4;break;case 4:if(l=JSON.stringify(n),c=this.responseCache.get(l),f=Date.now(),!c){e.next=16;break}if(f-c.requestTimestamp<1e3*a)return this.responseCache.delete(l),this.responseCache.set(l,c),e.abrupt("return",Promise.resolve(c.response));e.next=15;break;case 15:this.responseCache.delete(l);case 16:return e.next=18,r.call.apply(r,[t].concat((0,b.default)(n)));case 18:return h=e.sent,this.responseCache.set(l,{requestTimestamp:f,response:h}),this.responseCache.size>u&&(p=this.responseCache.keys().next().value,this.responseCache.delete(p)),e.abrupt("return",h);case 23:case"end":return e.stop()}},e,this)})),function(e,t,r,n){return o.apply(this,arguments)})}]),e}();r.default=o},{"@babel/runtime/helpers/asyncToGenerator":4,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10,"@babel/runtime/helpers/toConsumableArray":17,"@babel/runtime/regenerator":21}],90:[function(e,t,r){"use strict";var n=e("@babel/runtime/helpers/interopRequireDefault"),o=n(e("@babel/runtime/helpers/classCallCheck")),i=n(e("@babel/runtime/helpers/createClass"));Object.defineProperty(r,"__esModule",{value:!0}),r.SearchOnlyCollection=void 0;var a=e("./SearchOnlyDocuments"),s=function(){function n(e,t,r){(0,o.default)(this,n),this.name=e,this.apiCall=t,this.configuration=r,this._documents=new a.SearchOnlyDocuments(this.name,this.apiCall,this.configuration)}return(0,i.default)(n,[{key:"documents",value:function(){return this._documents}}]),n}();r.SearchOnlyCollection=s},{"./SearchOnlyDocuments":91,"@babel/runtime/helpers/classCallCheck":5,"@babel/runtime/helpers/createClass":7,"@babel/runtime/helpers/interopRequireDefault":10}]},{},[58])(58)});
//# sourceMappingURL=typesense.min.js.map -- https://cdn.jsdelivr.net/npm/typesense@1.1/dist/typesense.min.js