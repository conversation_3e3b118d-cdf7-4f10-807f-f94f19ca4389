:root {
    --primary-color: #0058a3;
    --secondary-color: #6c757d;
    --background-color: #f8f9fa;
    --text-color: #333;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #004c8c;
    border-color: #004c8c;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.card {
    border: none;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    will-change: transform;
    transform: translateZ(0);
    display: flex;
    height: 50px;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    background-color: #fff;
}

.mobile-bottom-nav__item {
    flex-grow: 1;
    text-align: center;
    font-size: 0.8rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mobile-bottom-nav__item-content {
    display: flex;
    flex-direction: column;
}

.part-badge {
    transition: transform 0.2s;
}

.part-badge:hover {
    transform: scale(1.1);
}

.page-pair {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 10px;
}

.page-pair img {
    max-width: calc(50% - 7.5px);
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

#tableOfContents {
    position: sticky;
    top: 20px;
}

#tableOfContents ul {
    list-style-type: none;
    padding-left: 0;
}

#tableOfContents li {
    margin-bottom: 10px;
}

#tableOfContents a {
    text-decoration: none;
    color: var(--text-color);
    transition: color 0.2s;
}

#tableOfContents a:hover {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    #tableOfContents {
        position: static;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@media print {
    .no-print {
        display: none;
    }
}

/* New styles for the autocomplete dropdown */
#searchAutocomplete {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    margin-top: 5px;
}

#searchAutocomplete .dropdown-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

#searchAutocomplete .dropdown-item:last-child {
    border-bottom: none;
}

#searchAutocomplete .dropdown-item:hover,
#searchAutocomplete .dropdown-item.active {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

#searchAutocomplete .dropdown-item .bg-warning {
    background-color: rgba(255, 193, 7, 0.3) !important;
    padding: 2px 0;
    border-radius: 2px;
}

#searchAutocomplete::-webkit-scrollbar {
    width: 8px;
}

#searchAutocomplete::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
}

#searchAutocomplete::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 8px;
}

#searchAutocomplete::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.card-link {
    display: block;
    color: inherit;
}

.card-link:hover {
    text-decoration: none;
    color: inherit;
}

.card-link:hover .card {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

h3.type-groups {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-top: 30px;
}

.list-group {
    margin-bottom: 20px;
}

.hover-highlight:hover {
    background-color: #f8f9fa;
}

.toc-scrollable {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding-right: 10px;
}

.toc-scrollable::-webkit-scrollbar {
    width: 6px;
}

.toc-scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.toc-scrollable::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.toc-scrollable::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.navbar-dark .navbar-nav .nav-link {
    color: white;
}

.store-section {
    scroll-margin-top: 1rem;
}