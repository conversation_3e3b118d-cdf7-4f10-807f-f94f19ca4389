document.addEventListener('DOMContentLoaded', function () {
    // validation for stock search bar
    var form = document.getElementById('stockSearchForm');
    var input = document.getElementById('stockSearchInput');
    var errorDiv = document.getElementById('searchError');

    form.addEventListener('submit', function (event) {
        var value = input.value.trim();

        if (value === '') {
            showError('Search field cannot be empty.');
            event.preventDefault();
        } else if (!/^[\d.]+$/.test(value)) {
            showError('Only numbers and dot (.) are allowed.');
            event.preventDefault();
        } else {
            errorDiv.style.display = 'none';
        }
    });

    function showError(message) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        input.classList.add('is-invalid');
    }

    input.addEventListener('input', function () {
        input.classList.remove('is-invalid');
        errorDiv.style.display = 'none';
    });

    // table's expandable row function
    var rows = document.querySelectorAll('.expandable-row');
    rows.forEach(function (row) {
        row.addEventListener('click', function () {
            var chevron = this.querySelector('.bi-chevron-down, .bi-chevron-up');
            chevron.classList.toggle('bi-chevron-down');
            chevron.classList.toggle('bi-chevron-up');
        });
    });
});