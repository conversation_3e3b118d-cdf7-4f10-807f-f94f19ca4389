import json, random
from my_ollama import generate_answer, generate_question
from question_services import QuestionAnswerServices
from faker import Faker
from datetime import datetime, timedelta

qna_service = QuestionAnswerServices()
with open('product_details.json', 'r') as f:
    data = json.load(f)
small_data = data[306:312]

def generate_random_name():
    fake = Faker()
    name_formats = [
        lambda: fake.first_name(),  # First name only
        lambda: fake.first_name() + " " + fake.last_name(),  # First + Last name
        lambda: fake.last_name() + " " + fake.first_name()  # Last + First name
    ]
    return random.choice(name_formats)()

def pick_random_replier_name():
    names = ['Lisa @ EasyRebuild', 'Alice @ EasyRebuild', 'Kenzo @ EasyRebuild']
    return random.choice(names)

def weighted_random_for_answer(max=1):
    return 0 if random.random() < 0.66 else random.randint(1, max)

def weighted_random_for_question(max=6):
    # Ensure that 0 has a 50% chance and max has a 1% chance
    weights = [60] + [15] +[14] + [10.5] + [1] + [0.3] + [0.2]
    return random.choices(range(max+1), weights=weights, k=1)[0]

def test_weighted_random_for_question():
    count_dict = {i: 0 for i in range(7)}
    for _ in range (10000):
        num = weighted_random_for_question()
        count_dict[num] += 1
    print(count_dict)


def replace_with_newline(text:str) -> str:
    if 'Thank you' in text:
        text = text.replace('Thank you', '\nThank you')
    elif '?' in text:
        text = text.replace('? ', '?\n')
        text = text.replace('Could you', '\nCould you')
        text = text.replace('Is there', '\nIs there')
    return text

def add_to_end(text:str, user_name:str) -> str:
    name = user_name.split(' ')[0] if user_name else ''
    formatting_changes = [
        lambda: text + f'\n{user_name}',
        lambda: text + f'\n{name}',
        lambda: text + '\nCheers!',
    ]
    text = random.choice(formatting_changes)()
    return text

def randomize_question_format(text:str, user_name='') -> str:
    print(f'Pre-formatting: {text}')
    # Define the possible formatting changes
    formatting_changes = [
        lambda: add_to_end(text, user_name),
        lambda: replace_with_newline(text)
    ]
    # Randomly select one formatting change and apply it
    text = random.choice(formatting_changes)()
    text = text.replace('[Your Name]', '')
    return text

def generate_random_date() -> datetime:
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2024, 3, 26)
    random_date = start_date + timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds())),
    )
    return random_date

def create_and_insert_questions(small_data):
    # test_weighted_random_for_question()
    for sd in small_data:
        desc = sd['details']
        fur_id = str(sd['id'])
        num_questions = weighted_random_for_question()
        print(f'@@@ start {num_questions} questions')
        for _ in range(num_questions):
            print('--- question: ---')
            asker = generate_random_name()
            question_text = generate_question(desc)
            question_text_formatted = randomize_question_format(question_text)
            print(f'Formatted: {question_text_formatted}')
            post_time = generate_random_date()
            qid = qna_service.insert_question(fur_id, asker, question_text_formatted, f'{asker}@example.com', post_time=post_time)
            num_answers = weighted_random_for_answer()
            print(f'\n@@@@@ start {num_answers} answers')
            for _ in range(num_answers):
                print(f'----- answer: -----')
                answer = generate_answer(desc, question_text_formatted)
                print(f'{answer}\n')
                replier = pick_random_replier_name()
                # answer 2 days later
                post_time = post_time + timedelta(days=2)
                qna_service.insert_answer(fur_id, qid, replier, answer, f'{asker}@example.com', post_time=post_time)

create_and_insert_questions(small_data)