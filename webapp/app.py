import json
import math, logging

from flask import Flask, render_template, request, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
from flask_caching import Cache
from waitress import serve

from search_services import SearchServices
from question_services import QuestionAnswerServices
from stock_services import Stock_Services
from static_constant import RESULT_TEMPLATE, COUNTRIES, COUNTRY_PARAM, PRELOAD_LIST, PART_SEARCH
from utils import parse_search_results, parse_product, get_optimal_thread_count, block_bots
from config import Config
from logger import init_logger


app = Flask(__name__)
app.config.from_object(Config)

############ ----- XXX MUST BE 'False' WHEN DEPLOYED ----- ############
app.debug = True

# set timeout for route caching in debug
timeout_1day = 86500
timeout_1hour = 3600
timeout_10min = 600
if app.debug:
    timeout_1day = 10
    timeout_1hour = 10
    timeout_10min = 10

# Set up logger
logger = init_logger(app)
logging.getLogger('webapp').setLevel(logging.DEBUG)
logger.warning(f'## App is running in debug? [{app.debug}] ##')

# Initialize extensions
mysql_db = SQLAlchemy(app)
cache = Cache(app)

# Initialize services
qna_service = QuestionAnswerServices()
search_service = SearchServices(mysql_db)
stock_service = Stock_Services(dev_mode=app.debug, proxy_user=app.config['PROXY_USER'], proxy_pass=app.config['PROXY_PASS'])
email_service = Mail(app)

@cache.cached(timeout=None)
@app.context_processor
def inject_static():
    return {
        'logo_url': app.config['LOGO_URL'],
        'ts_host': app.config['TS_HOST'],
        'ts_port': app.config['TS_PORT'],
        'ts_protocol': app.config['TS_PROTOCOL'],
        'ts_api_key': app.config['TS_API_KEY']
    }

def send_email(title, sender, recipients, body):
    msg = Message(title, sender=sender, recipients=recipients)
    msg.body = body
    email_service.send(msg)

##################### ROUTES #####################
@app.route('/sitemap.xml')
@cache.cached(timeout=timeout_10min)
def sitemap():
    # XXX comment out after generating sitemap so it won't be generated on every request
    # search_service.get_sitemap()
    return send_file('sitemap.xml')

@app.route('/robots.txt')
@cache.cached(timeout=timeout_10min)
def robots():
    return """
User-agent: *
Allow: /

User-agent: Amazonbot
Disallow: /

Sitemap: https://easyrebuild.com/sitemap.xml
"""

@app.errorhandler(404)
def not_found_error(error):
    logger.error(f'Page not found: {request.url}')
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f'Server Error: {error}', exc_info=True)
    return render_template('500.html'), 500

@app.route('/about')
@cache.cached(timeout=timeout_1day)
def about():
    title = 'About EasyRebuild'
    description = """
    EasyRebuild is a free online tool to search for IKEA assembly instruction manuals and furniture parts.
    Our mission is simple: to provide access to assembly instruction manuals of IKEA® furniture models that were discontinued. 
    We're passionate about sustainability and believe that reusing furniture is not only environmentally friendly but also practical and economical.
    """
    keywords = 'About EasyRebuild, IKEA Assembly Instruction Look up, IKEA Manual Database, IKEA parts lookup tool, IKEA Hardware parts search'
    return render_template('about.html', title=title, description=description, keywords=keywords)

@app.route('/add_question', methods=['POST'])
def add_question():
    furniture_id = request.form.get('furniture_id')
    user = request.form.get('user')
    text = request.form.get('text')
    email = request.form.get('email')
    qna_service.insert_question(furniture_id, user, text, email)
    current_url = request.form.get('currentUrl')
    title = f'Message from EasyRebuild - Question section sent by: {email}'
    sender = app.config['MAIL_USERNAME']
    body = f'URL: {current_url}\nBody: {text}'
    send_email(title, sender, [sender], body)
    return "Question added!"

@app.route('/add_answer', methods=['POST'])
def add_comment():
    furniture_id = request.form.get('furniture_id')
    question_id = request.form.get('question_id')
    user = request.form.get('user')
    text = request.form.get('text')
    email = request.form.get('email')
    qna_service.insert_answer(furniture_id, question_id, user, text, email)
    current_url = request.form.get('currentUrl')
    title = f'Message from EasyRebuild - Answer section sent by: {email}'
    sender = app.config['MAIL_USERNAME']
    body = f'URL: {current_url}\nBody: {text}'
    send_email(title, sender, [sender], body)
    return "Answer added!"

@app.route('/')
@cache.cached(timeout=timeout_1day)
def index():
    title = 'EasyRebuild - Search IKEA Manuals and Parts'
    description = 'EasyRebuild - your IKEA assembly instruction manuals and parts database. Free online lookup tool to search for your IKEA instructions and parts list by product name or number.'
    keywords = 'IKEA Assembly Instruction Look up, IKEA Manual Database, IKEA parts lookup, IKEA Hardware parts search'
    counts = search_service.get_total_counts()
    models = search_service.get_all_models_sorted()
    return render_template('index.html', 
                           total_products=counts['total_products'], 
                           total_parts=counts['total_parts'], 
                           models=models, 
                           title=title, description=description, keywords=keywords)

@app.route('/search', methods=['GET'])
@cache.cached(timeout=timeout_10min, query_string=True)
def search():
    # Check for bots
    should_block, status_code, message = block_bots(request)
    if should_block:
        return message, status_code

    page = request.args.get('page', 1, type=int)
    query = request.args.get('q', '').strip()
    search_type = request.args.get('searchType')
    per_page = 12
    results, total = search_service.get_search_results(query, search_type, page, per_page=per_page)
    
    title = f'{query} - Search EasyRebuild'
    description = f'Search Result page for IKEA {query} Assembly instructions and manuals'
    keywords = 'IKEA Assembly Instruction Look up, User Manual Database, IKEA parts lookup, IKEA Hardware parts search'
    if total == 0:
        return render_template(RESULT_TEMPLATE, 
                               search_results={}, q=query, search_type=search_type,
                               title=title, description=description, keywords=keywords)
    
    total_pages = math.ceil(total / per_page)
    results = parse_search_results(results)
    part_data = None
    if search_type == PART_SEARCH:
        part_data = search_service.get_part_data(query)
    return render_template(RESULT_TEMPLATE, 
                           q=query, 
                           search_results=results, 
                           part_data=part_data,
                           search_type=search_type, 
                           total=total, total_pages=total_pages,
                           title=title, description=description, keywords=keywords)

@app.route('/d/<id>/<stag>')
@cache.cached(timeout=timeout_10min)
def details(id, stag):
    # Check for bots
    should_block, status_code, message = block_bots(request)
    if should_block:
        return message, status_code

    query = request.args.get('q')
    results = search_service.search_details_by_fur_id(id)
    bad_query = request.url
    
    if not results:
        return render_template(RESULT_TEMPLATE, search_results={}, q=bad_query)

    product = parse_product(results)
    if product.product_num not in stag:
        return render_template(RESULT_TEMPLATE, search_results={}, q=bad_query)

    # convert to 123.456.78 format 
    pnum = product.product_num
    product.product_num = '.'.join([pnum[i:i+3] for i in range(0, len(pnum), 3)])
    name_type = f'{product.name} {product.product_type}'
    # get related products
    results = search_service.search_related_products(product.product_type)
    results = parse_search_results(results) if len(results) > 1 else []

    questions = qna_service.get_questions(id) or []

    title = f'IKEA {product.product_num} {name_type} Instruction Manual'
    description = f'Detail view of the IKEA {product.name} {product.product_num} assembly instruction manuals and parts list. Find out what needed to perfectly rebuild IKEA {product.name_w_type}'
    keywords = f'IKEA {product.name_w_type} pdf, IKEA {product.product_num}, part list, assembly instruction'
    return render_template('details.html', 
                           questions=questions,
                           product=product, 
                           name_type=name_type, id=id, q=query, 
                           search_results=results,
                           title=title, description=description, keywords=keywords)

@app.route('/models', methods=['GET'])
@cache.cached(timeout=timeout_1day)
def models():
    model_set = search_service.get_all_models_sorted()
    title = 'IKEA Instructions for all furniture models'
    description = 'Complete list of assembly instruction manuals and parts list for all IKEA furniture models'
    keywords = 'IKEA product catalogs, all IKEA manual collection, IKEA assembly instruction'
    return render_template('all_models.html', 
                           models=model_set, 
                           title=title, description=description, keywords=keywords)

@app.route('/models/<model_name>', methods=['GET'])
@cache.cached(timeout=timeout_10min, query_string=True)
def model(model_name):
    results = search_service.get_models(model_name)
    models = parse_search_results(results)

    title = f'IKEA Instructions for all {model_name} models'
    description = f'List of assembly instruction manuals and parts list for all IKEA {model_name} models'
    keywords = f'IKEA user manuals, IKEA assembly instruction for all {model_name} models, all IKEA {model_name} manuals'
    return render_template('models.html', 
                           q=model_name, 
                           models=models, 
                           title=title, description=description, keywords=keywords)

@app.route('/stock', methods=['GET'])
@cache.cached(timeout=timeout_10min, query_string=True)
def stock():
    itemNo = request.args.get('itemNo', '')
    cleanedItemNo = itemNo.replace('.', '').strip()
    country_code = request.args.get(COUNTRY_PARAM, '')
    country_name = COUNTRIES.get(country_code, {}).get('name')
    lang = COUNTRIES.get(country_code, {}).get('lang')
    availabilities = None
    product_info = None
    if itemNo and lang:
        product_info = stock_service.get_product_info_stock_check(country_code, cleanedItemNo)
        availabilities = stock_service.get_availabilities(cleanedItemNo, country_code, lang)
        title = f'IKEA Stock Checker for {itemNo} - EasyRebuild.com'
        description = f'Check stock availabilities of {itemNo} in every IKEA store in {country_name} - EasyRebuild.com'
    else:
        title = 'IKEA Stock Checker - EasyRebuild.com'
        description = f'EasyRebuild.com - Check the availability of IKEA products in every IKEA store worldwide'
    
    keywords = 'EasyRebuild IKEA stock checker, IKEA stock check, IKEA availability check, IKEA in store stock lookup'
    
    return render_template('stock.html', 
                           qItemNo=itemNo, qCountry=country_code,
                           product_info=product_info,
                           availabilities=availabilities,
                           country_name=country_name,  
                           countries=COUNTRIES,
                           title=title, description=description, keywords=keywords)

@app.route('/asis', methods=['GET'])
@cache.cached(timeout=timeout_10min, query_string=True)
def asis():
    country_code = request.args.get(COUNTRY_PARAM, '')
    country_name = COUNTRIES.get(country_code, {}).get('name')
    lang = COUNTRIES.get(country_code, {}).get('lang')
    all_asis_items = None
    logger.info(f'Request as is data for {country_name}')
    if country_name and lang:
        all_asis_items = stock_service.get_as_is_by_country(country_code, lang)
        # all_asis_items = get_asis_data()
        title = f'{country_name} As Is Section - EasyRebuild.com'
        description = f'Check as is stock in every IKEA store in {country_name} - EasyRebuild.com'
    else:
        title = 'IKEA As Is Finder - EasyRebuild.com'
        description = f'EasyRebuild.com - Check the as is section of IKEA in every IKEA store worldwide'

    keywords = 'EasyRebuild IKEA as is checker, IKEA bargain lookup, IKEA as is section, IKEA as is stock'
    return render_template('asis.html', 
                           qCountry=country_code,
                           all_asis_items=all_asis_items, 
                           country_name=country_name, 
                           countries=COUNTRIES, 
                           title=title, description=description, keywords=keywords)
    
def get_asis_data():
    with open('sample_as_is_us_items.json', 'r', encoding='utf-8') as f:
        return json.load(f)
    
if __name__ == '__main__':
    if app.debug:
        logger.info('--- Running with Flask development server ---')
        app.run(debug=True, host='0.0.0.0', port=5001) # mac os occupied :5000
    else:
        stock_service.schedule_daily_prefetch(PRELOAD_LIST, 1, 1, 30)
        threads = get_optimal_thread_count()
        logger.info(f'Running with Waitress production server - Optimal threads: {threads}')
        serve(app, host='0.0.0.0', port=5000, threads=threads)
