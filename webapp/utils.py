import json, re, os, logging
from unidecode import unidecode

from models_webapp import *
from static_constant import PNG_EXTENSION, JPG_EXTENSION

logger = logging.getLogger('webapp')

def block_bots(request):
    """
    Check and block unwanted bot traffic
    Returns tuple: (should_block: bool, status_code: int, message: str)
    """
    user_agent = request.headers.get('User-Agent', 'Unknown').lower()
    full_url = request.url
    referrer = request.referrer or 'Direct'
    
    # List of banned bots
    banned_bots = [
        'amazonbot',
        'semrushbot',
        'dotbot',
        'bytespider'
    ]
    
    # Allow major search engines
    allowed_bots = [
        'googlebot',
        'bingbot',
        'yandexbot',
        'duckduckbot'
    ]
    
    # Check if it's a bot
    bot_identifiers = ['bot', 'crawler', 'spider', 'http', 'python', 'scraper']
    is_bot = any(identifier in user_agent for identifier in bot_identifiers)
    
    if is_bot:
        # Allow major search engines
        if any(allowed in user_agent for allowed in allowed_bots):
            logger.info(f"Allowed bot request: URL: {full_url} | Bot: {user_agent} | Referrer: {referrer}")
            return False, 200, "Allowed"
            
        # Block banned bots
        if any(banned in user_agent for banned in banned_bots):
            logger.warning(f"Blocked bot request: URL: {full_url} | Bot: {user_agent} | Referrer: {referrer}")
            return True, 403, "Access Denied - Bot Traffic Not Allowed"
            
        # Log unknown bots
        logger.warning(f"Unknown bot request: URL: {full_url} | Bot: {user_agent} | Referrer: {referrer}")
    else:
        logger.info(f'Not bot! URL: {full_url} | UA: {user_agent} | Referrer: {referrer}')

    return False, 200, "Allowed"


def get_optimal_thread_count():
    cpu_count = os.cpu_count()
    if cpu_count is None:
        return 2
    return cpu_count * 4

def parse_json_field(field):
    if field == '[]':
        return []
    return json.loads(field)

def parse_product(results):
    NAME_IX = 0
    NAME_W_TYPE_IX = 1
    PDF_PATHS_IX = 2
    IMAGE_DIR_IX = 3
    PRODUCT_NUM_IX = 4
    DETAILS_IX = 5
    MEASUREMENTS_IX = 6
    PRODUCT_TYPE_IX = 7
    PAGE_COUNT_IX = 8
    PART_LIST_IX = 9
    PDF_FILE_NAME_IX = 10

    pdfs = [
        Pdf(
            file_name=parse_pdf_filename(row[PDF_FILE_NAME_IX]),
            pdf_path = create_img_url(row[PDF_PATHS_IX], f'p1.{PNG_EXTENSION}'),
            page_count=row[PAGE_COUNT_IX],
            parts = row[PART_LIST_IX].split(', ') if row[PART_LIST_IX] else []
        )
        for row in results
    ]

    return Product(
        name=results[0][NAME_IX],
        name_w_type=results[0][NAME_W_TYPE_IX],
        image_dir = create_img_url(results[0][IMAGE_DIR_IX], f'main_.{JPG_EXTENSION}'),
        product_num=results[0][PRODUCT_NUM_IX],
        details = parse_json_field(results[0][DETAILS_IX]),
        measurements = parse_json_field(results[0][MEASUREMENTS_IX]),
        product_type=results[0][PRODUCT_TYPE_IX],
        pdfs=pdfs
    )

def parse_pdf_filename(raw_filename: str) -> str:
    parts = raw_filename.split("__")
    return parts[0].replace("-", " ").capitalize()

def parse_search_results(results: List[Any]) -> List[SearchResult]:
    return [
        SearchResult(
            id=result[0],
            name=result[1],
            name_w_type=result[2],
            thumbnail=create_img_url(result[3], f'thumb_.{JPG_EXTENSION}'),
            stag=create_seo_tag(result[1], result[4], result[5]),
            product_num='.'.join([result[4][i:i+3] for i in range(0, len(result[4]), 3)]),
            product_type=result[5]
        )
        for result in results
    ]

def create_img_url(dir: str, filename: str) -> str:
    return f"{os.getenv('CLOUD_STORAGE_URL')}/{dir}/{filename}"

def create_seo_tag(name: str, product_num: str, product_type: str) -> str:
    try:
        converted = unidecode(name)
        output = f"ikea-instructions-{converted}-{product_type}-{product_num}"
        return re.sub(r'\W', '-', output).lower()
    except Exception as e:
        print(f"Error creating SEO tag: {e}")
        return 'na'