import ollama

LLM_MODEL = 'mistral-32k-fp16'

def generate_question(product_desc:str) -> str:
    system_prompt:str = f"""
    Read the product description below thoroughly and write a comment to ask for help troubleshooting a problem about the product.
    """

    example1:str = f"""
    The Ikea DALSKAR faucet is a bathroom fixture designed to provide reliable and durable performance. Crafted with high-quality materials, this faucet is built to withstand everyday use and maintain its functionality over time. 
    The DALSKAR faucet features a sleek and modern design, making it a stylish addition to any bathroom decor. With a chrome finish, it adds a touch of sophistication to the overall aesthetic.
    This faucet is designed for easy installation, making it convenient for both professional plumbers and DIY enthusiasts. With its simple and straightforward design, it can be easily integrated into any bathroom sink system.
    The DALSKAR faucet is equipped with a single handle, allowing for seamless temperature and water flow control. This ergonomic design ensures comfort and ease of use for the user.
    Additionally, this faucet is equipped with a high-arc spout, providing ample clearance for various bathroom tasks such as washing hands or filling up containers.
    The DALSKAR faucet also incorporates water-saving features, helping to reduce water consumption and promote eco-friendly practices.
    In conclusion, the Ikea DALSKAR faucet is a reliable and durable bathroom fixture with a sleek design and user-friendly features. Its high-quality construction ensures longevity, making it a practical and stylish addition to any bathroom.
    """
    example2 = """
    The Ikea STRANDMON sofa is a seating option that offers comfort and style. Designed with a classic wingback shape, it adds a touch of elegance to any living space. The sofa comes in a variety of colors, making it easy to find one that matches your decor. The sturdy construction of the sofa ensures its durability, allowing it to withstand regular use for years to come.
    """
    example3="""
    The Ikea HATTEFJALL office chair is a seating solution designed for comfortable daily use. It features adjustable seat height control, tilt tension and a tilt lock function, offering customizable settings to support the user’s preferences and work style. The seat and backrest are made of molded foam and synthetic fiber, providing a soft and supportive surface. The chair also features armrests that are adjustable in height and width, which can be useful for finding the right ergonomic position and reducing strain on the arms and shoulders. The chair is constructed with sturdy materials for durability, and has a weight capacity of up to 243 pounds. The base is made of powder-coated aluminum and steel, and is equipped with casters for easy movement around the office space. The upholstery is made of synthetic leather or fabric, depending on the chosen design. The HATTEFJALL chair is available in different colors and sizes, providing versatility and customization options to fit various interior styles and individual preferences.
    """

    stream = ollama.chat(
        model=LLM_MODEL,
        # model='mixtral-rp',
        messages=[{'role': 'system', 'content': system_prompt}, 
                {'role': 'user', 'content': example1}, 
                {'role': 'assistant', 'content': 'I need a replacement cartridge (#124499) for my Dalskar bathroom faucet, which is 8 years old. How do I obtain a free replacement?'},
                {'role': 'assistant', 'content': 'How do you loosen the screw located above the handle at the back to adjust the faucet to the left and right?'},
                {'role': 'assistant', 'content': 'There is an Ikea Dalskar sink in our bathroom and we would like to remove it. How do you remove the sink drain?'},
                {'role': 'assistant', 'content': 'The faucet handle is getting almost impossible to move in any direction. Very hard on my hands.. What can I do?'},
                {'role': 'assistant', 'content': 'The faucet handle is very hard to move either way. Feels stiff. what can I do to fix this?'},
                {'role': 'assistant', 'content': 'Our dalskar bathroom faucet is starting to leak,where canI get anew valve?'},
                {'role': 'user', 'content': example2}, 
                {'role': 'assistant', 'content': 'I require a missing piece please, the right small screw. I require it for the other side of the wing chair, is it possible to have this sent to me?'},
                {'role': 'assistant', 'content': 'The side parts cannot be hooked in...is there a trick to it?'},
                {'role': 'user', 'content': example3}, 
                {'role': 'assistant', 'content': "What is the upright tilt degree of the hattefjall chair? what about the other tilts? how many, how far back do they go, and do they stay in place with one's own weight? (since apartently you can't lock those)."},
                {'role': 'assistant', 'content': 'Can I wash the cover in this chair? I wiped a tiny mark with fabric cleaner and it’s created a water mark. Can the whole cover unzip and be machine washed? Thank you'},
                {'role': 'assistant', 'content': 'Unfortunately, the seat height adjustment does not work. To return the chair, I would like to disassemble it again. This is also no longer possible, as the connecting piece between the seat and the chair wheels is so firmly stuck in the seat heating that it no longer comes out. Is there a trick to make this possible?'},
                {'role': 'assistant', 'content': 'Hello, how does the locking mechanism for the backrest work?'},
                {'role': 'user', 'content': product_desc},
                ],
        stream=True,
    )
    output = ""
    for chunk in stream:
        # print(chunk['message']['content'], end='', flush=True)
        output += chunk['message']['content']
    return output

def generate_answer(product_desc:str, question:str) -> str:
    system_prompt:str = """
    You are an AI assistant trained to provide support for IKEA products. 
    The user will ask questions about various IKEA products. 
    Follow the user's questions carefully & to the letter.
    You will provide accurate and helpful responses to the user's questions. 
    Remember, your goal is to assist the user in resolving their issues with these products. 
    Make sure it is relevant to the product description.
    Make it within 200 words
    """

    user_prompt=f"""
    <product description>
    {product_desc}

    <question>
    {question}

    <your response>
    """
    
    stream = ollama.chat(
        model=LLM_MODEL,
        messages=[{'role': 'system', 'content': system_prompt}, 
                {'role': 'user', 'content': user_prompt}],
        stream=True,
    )
    output = ""
    for chunk in stream:
        # print(chunk['message']['content'], end='', flush=True)
        output += chunk['message']['content']
    return output