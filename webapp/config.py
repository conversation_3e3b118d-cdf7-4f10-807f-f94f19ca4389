import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SQLALCHEMY_DATABASE_URI = f'mysql+mysqlconnector://{os.getenv("USER")}:{os.getenv("PASSWORD")}@{os.getenv("HOST")}:{os.getenv("PORT")}/{os.getenv("DATABASE")}'
    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False
    MAIL_USERNAME = os.getenv("EMAIL")
    MAIL_PASSWORD = os.getenv("EMAIL_P")

    CACHE_TYPE = 'SimpleCache'
    CACHE_DEFAULT_TIMEOUT = 60

    LOGO_URL = f'{os.getenv("CLOUD_STORAGE_URL")}/static/logo.png'
    TS_HOST = os.getenv("TS_HOST")
    TS_PORT= os.getenv("TS_PORT")
    TS_PROTOCOL= os.getenv("TS_PROTOCOL")
    TS_API_KEY= os.getenv("TS_API_KEY_RO")

    PROXY_USER = os.getenv("PROXY_USER")
    PROXY_PASS = os.getenv("PROXY_PASS")
    