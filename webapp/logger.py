import logging
from logging.handlers import TimedRotatingFileHandler
import os

def init_logger(app):
    # Determine if running in Docker
    in_docker = os.path.exists('/.dockerenv')
    
    if in_docker:
        log_dir = '/code/logs'
    else:
        # Get absolute path to application root directory for local development
        app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        log_dir = os.path.join(app_root, 'logs')
    
    print(f"Environment: {'Docker' if in_docker else 'Local'}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Attempting to create log directory at: {log_dir}")
    
    try:
        # Create logs directory with proper permissions
        os.makedirs(log_dir, mode=0o755, exist_ok=True)
        print(f"Successfully created log directory at: {log_dir}")
    except Exception as e:
        print(f"Failed to create log directory: {str(e)}")
        raise

    log_file = os.path.join(log_dir, 'webapp.log')
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(module)s.%(funcName)s] - %(message)s')

    # Set up timed rotating file handler 
    file_handler = TimedRotatingFileHandler(
        log_file,
        when='midnight', 
        interval=1,
        backupCount=30, # Keep 30 days of logs
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)

    # Set up size-based rotation
    file_handler.maxBytes = 10 * 1024 * 1024  # 10 MB

    # Configure root logger
    root_logger = logging.getLogger('webapp')
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)

    # Configure Flask app logger
    app.logger.setLevel(logging.DEBUG)
    app.logger.addHandler(file_handler)

    # Add console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)

    # Log the directory being used
    root_logger = logging.getLogger('webapp')
    root_logger.info(f"Logging to directory: {log_dir}")

    return root_logger
