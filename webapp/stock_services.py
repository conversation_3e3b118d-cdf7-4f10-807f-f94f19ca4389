import random
from curl_cffi import requests as curequest
from curl_cffi.requests import BrowserType
from typing import List, Dict, Any
from unidecode import unidecode
import concurrent.futures
import schedule, threading
import time
from datetime import datetime
import logging
from static_constant import COUNTRIES

logger = logging.getLogger('webapp')

class Stock_Services:
    _scheduler_thread = None  # Class variable to track the scheduler thread

    def __init__(self, dev_mode: bool = False, proxy_user: str = 'N/A', proxy_pass: str = 'N/A') -> None:
        self.world_stores = None
        self.dev_mode = dev_mode
        self.proxy_user = proxy_user
        self.proxy_pass = proxy_pass
        logger.info(f'Initializing Stock_Services with dev_mode={dev_mode}, Last 6 chars of proxy_user: {proxy_user[-3:]}, proxy_pass: {proxy_pass[-3:]}')


    def _fetch_availability_json(self, itemNo:str, country:str):
        url = f"https://api.ingka.ikea.com/cia/availabilities/ru/{country}?itemNos={itemNo}&expand=StoresList,Restocks"
        headers = {
        'accept': 'application/json;version=2',
        'accept-language': 'en-US,en;q=0.9',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-client-id': 'b6c117e5-ae61-4ef5-b4cc-e0b1e37f0631',
        'Cookie': '_abck=681C7EF33CE9D55EAE1F26A876820B6E~-1~YAAQNTovFxrSl7+SAQAAOJ3REQxcsNmzrSBndmSXm2in2P23tqLs3X0jOytY5e1WKd3ZN2fVuB26kUgmukKgedTwUhLA2U95KPXcSXpFy1b5hHYFevXF3cdCRJ+vFtJnO8vwIKQMNY3Thk+4QGFvLvM/D/Tm4q/y9KSv5r1LfhWGfUuElEdAQRcJvmgYjKXG8YkSGitPQbCgWg7ax9oEdj3OoiGWpqjkLsalleOQ7Zn8R6+KVfxwfMIqQZO/sauYvs9RgnKUADyCCpvC9E7CPEMJF9WKLssRg0HyOipbXZy1ri39BhgSMRObWhFi5drc52uXhIKM72/OH6hLjy3h4HprFRks/Pa72GbP4ZIKxs8hSvMs7g8GlKJ73K6qfSM/0whZ9XwtJyhdIFJletjetBcqgA9V4A7g3wvp4j+wqWuCH4FrG7K799QQt9YCZ/ijujASWtUaSt8=~-1~-1~1728753387; bm_sz=823177114645A6CCB4C9A33FB1A99EE7~YAAQNTovFxzSl7+SAQAAOJ3RERkhAJBzo87LJyCR+0jPUdxvSkv/P3gIwkMzyfIsbobRuG68q1vnwOiz8Wbp1uotFkBUMTdvd0m5UJxM5skzOjGSpG0LC44OwVhYCJ6M/wqfivGcJpRW2WZpSiWWe7Y1rtnfJT/bDpE+ah6D0O/sA/XVnL9+F3CMK7eHylLbE//+33R4GGzZCTWFkdqdUYoDuX/fYXMjjGE5ChvEokn40P6ybjmFUZz4Q+5J5Qudck9YfyHR8jF9daWUrHvAgYIbEm+JP8gYOdyKdDO6gsq9saVmPLAESE/q9nfDrMAnx8aSRxCGslxinicpz1YbqQ8b7DtHGAH9F10imXlD~4601144~3425603; ak_bmsc=CE096F3DE587F40CC4D880358DD8320F~000000000000000000000000000000~YAAQNTovF7MUor+SAQAA0gMkEhnLS6z5Wp5bCpIIbEboBDQl/L9Gte6v+YrhebhxgMZDDxsk9YTH4ce37orRrUscUJ22f4TnbeRhdYEWdmQbUW/4298HFHUfHjY9cPrr1G4NH12N4OhA3JHyiqTkgeYRfHY7yPGw6Wt5+QKbjU5NOPryzDW6bvyLZtb3SG89NCpQ5XJRLtmwY0tQwypaZ9IS9J9OCp3RowusE0B/T84xlJaYUA07aErnva+kd0UIew6/OeV49EZrZpo+3wIiPsTeZ5AZRw/5CRQjNs1NS2sGcX2TWiQsiGL/XF7VzIa0oxeHmWLLx+5N4/lFvQ7h/QnDYTu4xKabShfQG1N8CXgQe9nkKVoxa12hpGKx17Rc; bm_sv=D8CF3A9A8A6C3506E98270FD2601AC91~YAAQNTovF7QUor+SAQAA0gMkEhnZ66pVU6WAMgrebyx5Tn45ilgAwj7QiNfyelGinxM2d6jRPu1hivjAMcMWKByd+Q+pwLSeeWXTiyfAhtN1JpyU6+IpEod1y0WuBX9GjJw1MRxOBEc0luX8CrFhS+2Y12hqZEh364KYRPsbBjk382c3UYnxsMEFGVLgYdnQqq9aL3AUdPIdO3GlwTHz8p2SGPYAzHV1vPXwY0Nv/h0GiAfwBWnsEIoZfb9+WTzF1+7u7g==~1'
        }
        logger.debug(f'Fetching availability for item {itemNo} in country {country}')
        proxies = self._setup_proxy()
        try:
            response = curequest.get(url, headers=headers, proxies=proxies)
            response.raise_for_status()  
            return response.json()
        except Exception as e:
            logger.error(f"Error getting stock check for {itemNo}: {e}")
            return None


    def _fetch_store_details_json(self, country:str, lang:str):
        # need a dict to look up language; ex: us:en, hu:hu
        # url = f"https://www.ikea.com/{country}/{lang}/meta-data/navigation/stores-detailed.json" not working
        url = f"https://www.ikea.com/{country}/{lang}/meta-data/informera/stores-detailed.json"
        
        headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'text/plain; charset=utf-8',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': '_abck=681C7EF33CE9D55EAE1F26A876820B6E~-1~YAAQNTovFxrSl7+SAQAAOJ3REQxcsNmzrSBndmSXm2in2P23tqLs3X0jOytY5e1WKd3ZN2fVuB26kUgmukKgedTwUhLA2U95KPXcSXpFy1b5hHYFevXF3cdCRJ+vFtJnO8vwIKQMNY3Thk+4QGFvLvM/D/Tm4q/y9KSv5r1LfhWGfUuElEdAQRcJvmgYjKXG8YkSGitPQbCgWg7ax9oEdj3OoiGWpqjkLsalleOQ7Zn8R6+KVfxwfMIqQZO/sauYvs9RgnKUADyCCpvC9E7CPEMJF9WKLssRg0HyOipbXZy1ri39BhgSMRObWhFi5drc52uXhIKM72/OH6hLjy3h4HprFRks/Pa72GbP4ZIKxs8hSvMs7g8GlKJ73K6qfSM/0whZ9XwtJyhdIFJletjetBcqgA9V4A7g3wvp4j+wqWuCH4FrG7K799QQt9YCZ/ijujASWtUaSt8=~-1~-1~1728753387; ak_bmsc=3D6EDFD7DC06A52FA91C582187AB940B~000000000000000000000000000000~YAAQncpNF2TZtgWTAQAANts3Ehkj9zAe80bcFByHVeVvCPrxC8ZyyztUI7wKRt3hb274crfCvUM5qegKZb1HqiRE400TtnHetQmVoklhG/mcUrW9g6P2gyhOxQJyQGD2Tuz/ymPv99UtX31N/EHyTspVL1TokxBJNY5HARrDauKok0GKXZeouAv8R5C382CKQgnT0ecMesw5bc6vsTeqFqFM4R8g5hiXLorF2yapRI+0/AGYtumU1tTecP/uJ2R/njAZeMFd/fmYNOmD6QQQnpK8OuHhBIVjijeK0cmbFUtiTz6ZAKJpBMVBjYrGQhXo+RuMvqr4vJTaGiwgrs6oWkaMn2nfZz1RX2w=; bm_sv=7E3B19B0A5909DEDF8AFC9486E38C0B2~YAAQbspNF1F9NgqTAQAAUXM5EhkzYXqx1sc8PvTxeBkreszj6RIeaKHEuqBUsp2Qmjw4FiKWP6kgKnufKvWwhs9gXi5sR1GbwD26Eq+s8/yZgzC88KVJRwzYVw6DLoovCjdAMQ5UsiuxDiZ50vTVJUod5rHvsaRJWgmE1O4xPC+NqzgmQ7wusO84jrAMuGZHg6Yvxqfon7fkLGbz+chYHdRBObslVMneC/ZZ8A0w8fxBVguWEuIOKsIC35J4OQ==~1; bm_sz=823177114645A6CCB4C9A33FB1A99EE7~YAAQNTovFxzSl7+SAQAAOJ3RERkhAJBzo87LJyCR+0jPUdxvSkv/P3gIwkMzyfIsbobRuG68q1vnwOiz8Wbp1uotFkBUMTdvd0m5UJxM5skzOjGSpG0LC44OwVhYCJ6M/wqfivGcJpRW2WZpSiWWe7Y1rtnfJT/bDpE+ah6D0O/sA/XVnL9+F3CMK7eHylLbE//+33R4GGzZCTWFkdqdUYoDuX/fYXMjjGE5ChvEokn40P6ybjmFUZz4Q+5J5Qudck9YfyHR8jF9daWUrHvAgYIbEm+JP8gYOdyKdDO6gsq9saVmPLAESE/q9nfDrMAnx8aSRxCGslxinicpz1YbqQ8b7DtHGAH9F10imXlD~4601144~3425603'
        }
        logger.debug(f'Fetching store details for {country}/{lang}')
        try:
            response = curequest.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting store details for {country}: {e}")
            return None    
        
    def _setup_proxy(self):
        list_of_proxies = [
            'atlanta.us.socks.nordhold.net',
            'dallas.us.socks.nordhold.net',
            'los-angeles.us.socks.nordhold.net',
            'us.socks.nordhold.net',
            'new-york.us.socks.nordhold.net',
            'san-francisco.us.socks.nordhold.net',
            'detroit.us.socks.nordhold.net'
        ]
        # Get one proxy server at random
        proxy_server = random.choice(list_of_proxies)
        proxy_port = '1080'
        
        # Configure the proxy for the request
        proxies = {
            'http': f'socks5h://{self.proxy_user}:{self.proxy_pass}@{proxy_server}:{proxy_port}',
            'https': f'socks5h://{self.proxy_user}:{self.proxy_pass}@{proxy_server}:{proxy_port}'
        }
        
        return proxies

    def _fetch_product_details_json(self, country:str, itemNo:str):
        url = f"https://api.ingka.ikea.com/salesitem/communications/ru/{country}?itemNos={itemNo}"
        headers = {
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Referer': 'https://www.ikea.com/',
        'x-client-id': '0b0cb44f-194c-42eb-a996-4cc165bd902a',
        'sec-ch-ua-mobile': '?0',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sec-ch-ua-platform': '"Windows"',
        'Cookie': '_abck=681C7EF33CE9D55EAE1F26A876820B6E~-1~YAAQNTovFxrSl7+SAQAAOJ3REQxcsNmzrSBndmSXm2in2P23tqLs3X0jOytY5e1WKd3ZN2fVuB26kUgmukKgedTwUhLA2U95KPXcSXpFy1b5hHYFevXF3cdCRJ+vFtJnO8vwIKQMNY3Thk+4QGFvLvM/D/Tm4q/y9KSv5r1LfhWGfUuElEdAQRcJvmgYjKXG8YkSGitPQbCgWg7ax9oEdj3OoiGWpqjkLsalleOQ7Zn8R6+KVfxwfMIqQZO/sauYvs9RgnKUADyCCpvC9E7CPEMJF9WKLssRg0HyOipbXZy1ri39BhgSMRObWhFi5drc52uXhIKM72/OH6hLjy3h4HprFRks/Pa72GbP4ZIKxs8hSvMs7g8GlKJ73K6qfSM/0whZ9XwtJyhdIFJletjetBcqgA9V4A7g3wvp4j+wqWuCH4FrG7K799QQt9YCZ/ijujASWtUaSt8=~-1~-1~1728753387; bm_sz=823177114645A6CCB4C9A33FB1A99EE7~YAAQNTovFxzSl7+SAQAAOJ3RERkhAJBzo87LJyCR+0jPUdxvSkv/P3gIwkMzyfIsbobRuG68q1vnwOiz8Wbp1uotFkBUMTdvd0m5UJxM5skzOjGSpG0LC44OwVhYCJ6M/wqfivGcJpRW2WZpSiWWe7Y1rtnfJT/bDpE+ah6D0O/sA/XVnL9+F3CMK7eHylLbE//+33R4GGzZCTWFkdqdUYoDuX/fYXMjjGE5ChvEokn40P6ybjmFUZz4Q+5J5Qudck9YfyHR8jF9daWUrHvAgYIbEm+JP8gYOdyKdDO6gsq9saVmPLAESE/q9nfDrMAnx8aSRxCGslxinicpz1YbqQ8b7DtHGAH9F10imXlD~4601144~3425603; ak_bmsc=CE096F3DE587F40CC4D880358DD8320F~000000000000000000000000000000~YAAQNTovFxvSl7+SAQAAOJ3RERmvkQFSwwoep2/spxAjBsPb3FX2HIRZ1tR4GT1++IXoBoB7HterT+Ja+r/ZM0c/xUzGxr/qL+KL3N7ywuYJKI4shpQF6BBnGDtQMLhLZMVKyipfNrUHx8tRoTbLUcq4Xlck4C8uhBLSsJJSxOrr6C9A/BlJYA4W2dybHOOSoRefnWGAE5fVxABrWypikjC2Cqvalcr5pT5tE7wn48hShw+YFzII4+OdkHpWNTR7MTnU7bmROrRP2+vDfkMCHZ5M9Ddtpx0nxyqYeB0UOBPP/N5A9y6Blv9HnMmBjkkD7srhFA1pfsRt9KtQI8E2KXC64n3wasHjpc3QD/Dx7Ow=; bm_sv=D8CF3A9A8A6C3506E98270FD2601AC91~YAAQNTovF9val7+SAQAAYUDSERkTsGPMtcaiWtj5dCWNzKSI2+MW741nDkHqM2vhIj50UmFvhK0+It3uko7Kwoubi7mj7C+LQuRvaSXE8z8aBoXkHEPd3+zGrLFvEJNzlmTTIMPBZ49iR9PBisDi1F5OlIoLalhQHucRZ3H5BOeOjGchHca0nClrrKYOLVWYzeKC+/KYzda8U6Gxd1lX2IFMh1yIJKKIiOHraQWghvbM5rs/MIm0NbYAGW8Rp7C2x3iH5A==~1'
        }
        logger.debug(f'Fetching product details for {country}/{itemNo}')
        proxies = self._setup_proxy()
        try:
            # ip_check_response = curequest.get("https://api.ipify.org?format=json", proxies=proxies)
            # logger.debug(f"IP through proxy: {ip_check_response.json()['ip']}")
            response = curequest.get(url, headers=headers, proxies=proxies, timeout=15)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting product details for {country}: {e}")
            return None  
        
    
    def _extract_product_info(self, data):
        for product in data.get('data', []):
            for comm in product.get('localisedCommunications', []):
                main_image = None
                for media in comm.get('media', []):
                    alt_text = media.get('altText', '')
                    if media.get('typeName') == 'MAIN_PRODUCT_IMAGE':
                        for variant in media.get('variants', []):
                            if variant.get('quality') == 'S3':
                                main_image = variant.get('href')
                                break
                
                return {
                    'image': main_image,
                    'title': alt_text
                }
    

    def get_product_info_stock_check(self, country:str, itemNo:str):
        data = self._fetch_product_details_json(country, itemNo)
        return self._extract_product_info(data)
        

    def _get_stores_by_country(self, country: str, lang: str) -> dict:
        if self.world_stores is None:
            self.world_stores = {}
        if country not in self.world_stores:
            logger.info(f'Fetching store details for new country: {country}')
            stores = self._get_store_details(country, lang)
            self.world_stores[country] = stores
        return self.world_stores[country]
    

    def _get_store_details(self, country: str, lang: str)-> dict:
        data = self._fetch_store_details_json(country, lang)
        if data is None:
            return {}

        store_details = {}
        try:
            for store in data:
                store_id = store.get('id')
                if store_id:
                    store_details[store_id] = {
                        'displayNameAlternate': store.get('displayNameAlternate'),
                        'displayAddress': store.get('address', {}).get('displayAddress'), 
                        'state': store.get('address', {}).get('stateProvinceCode'),
                        'zipCode': store.get('address', {}).get('zipCode', ''),
                        'storePageUrl': store.get('storePageUrl')
                    }
            # sort list by state
            if country.lower() == 'us': 
                store_details = dict(sorted(
                    store_details.items(),
                    key=lambda x: x[1]['state']
                ))
            return store_details

        except Exception as e:
            logger.error(f"Error processing store details for {country}: {e}")
            return {}


    def get_availabilities(self, item_no: str, country: str, lang: str)-> list:
        result = self._fetch_availability_json(item_no, country)
        if result is None:
            return []
        
        stores = self._get_stores_by_country(country, lang)
        output = []

        # Create availability lookup dict for O(1) access
        avail_lookup = {
            avail.get('classUnitKey', {}).get('classUnitCode'): avail 
            for avail in result.get('availabilities', [])
        }
        
        # Process stores in their sorted order
        for store_id, store_info in stores.items():
            # Skip if no availability data for this store
            if store_id not in avail_lookup:
                continue
                
            avail = avail_lookup[store_id]
            buying_option = avail.get('buyingOption', {})
            
            avail_output = {
                'id': store_id,
                'name': store_info['displayNameAlternate'],
                'address': store_info['displayAddress'],
                'zipCode': store_info['zipCode'], 
                'storePageUrl': store_info['storePageUrl'],
                'is_pickup': avail.get('availableForCashCarry', False),
                'is_click_collect': avail.get('availableForClickCollect', False),
                'is_home_delivery': buying_option.get('homeDelivery', {}).get('range', {}).get('inRange', False),
                'quantity': 0,
                'earliestRestock': None,
                'latestRestock': None,
                'restockQuantity': 0
            }
            
            availability = buying_option.get('cashCarry', {}).get('availability', {})
            if avail_output['is_pickup']:
                avail_output['quantity'] = availability.get('quantity', 0)
            else:
                restocks = availability.get('restocks', [])
                if restocks:
                    restock_data = restocks[0]
                    avail_output['earliestRestock'] = restock_data.get('earliestDate')
                    avail_output['latestRestock'] = restock_data.get('latestDate')
                    avail_output['restockQuantity'] = restock_data.get('quantity', 0)
            
            output.append(avail_output)
        
        return output


    def _fetch_as_is_json(self, country: str, lang: str, storeId: str) -> List[Dict[str, Any]]:
        base_url = f"https://web-api.ikea.com/circular/circular-asis/offers/public/{country}/{lang}"
        headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': '_abck=681C7EF33CE9D55EAE1F26A876820B6E~-1~YAAQNTovFxrSl7+SAQAAOJ3REQxcsNmzrSBndmSXm2in2P23tqLs3X0jOytY5e1WKd3ZN2fVuB26kUgmukKgedTwUhLA2U95KPXcSXpFy1b5hHYFevXF3cdCRJ+vFtJnO8vwIKQMNY3Thk+4QGFvLvM/D/Tm4q/y9KSv5r1LfhWGfUuElEdAQRcJvmgYjKXG8YkSGitPQbCgWg7ax9oEdj3OoiGWpqjkLsalleOQ7Zn8R6+KVfxwfMIqQZO/sauYvs9RgnKUADyCCpvC9E7CPEMJF9WKLssRg0HyOipbXZy1ri39BhgSMRObWhFi5drc52uXhIKM72/OH6hLjy3h4HprFRks/Pa72GbP4ZIKxs8hSvMs7g8GlKJ73K6qfSM/0whZ9XwtJyhdIFJletjetBcqgA9V4A7g3wvp4j+wqWuCH4FrG7K799QQt9YCZ/ijujASWtUaSt8=~-1~-1~1728753387; ak_bmsc=3D6EDFD7DC06A52FA91C582187AB940B~000000000000000000000000000000~YAAQncpNF2TZtgWTAQAANts3Ehkj9zAe80bcFByHVeVvCPrxC8ZyyztUI7wKRt3hb274crfCvUM5qegKZb1HqiRE400TtnHetQmVoklhG/mcUrW9g6P2gyhOxQJyQGD2Tuz/ymPv99UtX31N/EHyTspVL1TokxBJNY5HARrDauKok0GKXZeouAv8R5C382CKQgnT0ecMesw5bc6vsTeqFqFM4R8g5hiXLorF2yapRI+0/AGYtumU1tTecP/uJ2R/njAZeMFd/fmYNOmD6QQQnpK8OuHhBIVjijeK0cmbFUtiTz6ZAKJpBMVBjYrGQhXo+RuMvqr4vJTaGiwgrs6oWkaMn2nfZz1RX2w=; bm_sz=823177114645A6CCB4C9A33FB1A99EE7~YAAQNTovFxzSl7+SAQAAOJ3RERkhAJBzo87LJyCR+0jPUdxvSkv/P3gIwkMzyfIsbobRuG68q1vnwOiz8Wbp1uotFkBUMTdvd0m5UJxM5skzOjGSpG0LC44OwVhYCJ6M/wqfivGcJpRW2WZpSiWWe7Y1rtnfJT/bDpE+ah6D0O/sA/XVnL9+F3CMK7eHylLbE//+33R4GGzZCTWFkdqdUYoDuX/fYXMjjGE5ChvEokn40P6ybjmFUZz4Q+5J5Qudck9YfyHR8jF9daWUrHvAgYIbEm+JP8gYOdyKdDO6gsq9saVmPLAESE/q9nfDrMAnx8aSRxCGslxinicpz1YbqQ8b7DtHGAH9F10imXlD~4601144~3425603'
        }
        all_store_items = []
        page = 0
        total_pages = 1  # Initialize to 1, will be updated in the first request

        if self.dev_mode:
            size = 6
            max_pages = 1
        else:
            size = 64
            max_pages = 100

        while page < total_pages and page < max_pages:
            url = f"{base_url}?size={size}&stores={storeId}&page={page}"
            try:
                response = curequest.get(url, headers=headers)
                response.raise_for_status()
                data = response.json()
                
                # Update total_pages if it's the first request
                if page == 0:
                    total_pages = data['totalPages']
                
                # Extend the all_store_items list with the current page's content
                all_store_items.extend(data['content'])
                
                # Move to the next page
                page += 1
            except Exception as e:
                logger.error(f"An error occurred while fetching page {page}: {e}")
                break

        logger.debug(f"Total items fetched from storeId {storeId}: {len(all_store_items)}")
        time.sleep(.16)
        return all_store_items


    def _process_store_items(self, store_items, store_id:str, country:str):
        for store_item in store_items:
            # process unicode for easy search
            store_item['title'] = unidecode(store_item['title'])
            item_id = store_item.get('id')
            # process as is item url
            asis_url = COUNTRIES[country].get("asis_url")
            if asis_url is not None:
                item_url = f'{COUNTRIES[country].get("asis_url")}/{store_id}/{item_id}'
            store_item['item_url'] = item_url
        # TODO sort lowest price
        return store_items
    
        
    def _fetch_and_process_store_items(self, country: str, lang: str, store_id: str):
        logger.debug(f'Fetching and processing as-is items for store {store_id}')
        store_items = self._fetch_as_is_json(country, lang, store_id)
        return self._process_store_items(store_items, store_id, country)


    def _fetch_store_items_parallel(self, country_stores: dict, country: str, lang: str) -> dict:
        # Create thread pool to fetch store items in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            
            # Submit tasks for stores
            for store_id, store_details in country_stores.items():
                if 'storeItems' not in store_details:
                    future = executor.submit(
                        self._fetch_and_process_store_items,
                        country,
                        lang, 
                        store_id
                    )
                    futures.append((store_id, future))
            
            # Wait for all futures to complete and update store details  
            for store_id, future in futures:
                try:
                    store_items = future.result()
                    country_stores[store_id]['storeItems'] = store_items
                except Exception as e:
                    logger.error(f"Error fetching items parallel for store {store_id}: {e}")
                    country_stores[store_id]['storeItems'] = []
                    
        return country_stores


    def get_as_is_by_country(self, country: str, lang: str):
        logger.debug(f'Get as-is data for country: {country} - lang: {lang}')
        country_stores = self._get_stores_by_country(country, lang)
        return self._fetch_store_items_parallel(country_stores, country, lang)


    def _prefetch_as_is_data(self, countries_and_langs: List[tuple]) -> None:
        try:
            for country, lang in countries_and_langs:
                logger.debug(f"Prefetching as-is data for {country}/{lang}")
                self.get_as_is_by_country(country, lang)
                sleep_time = 300
                logger.debug(f'Finished prefetching. Sleeping for {sleep_time}s to avoid rate limit')
                time.sleep(sleep_time)
        except Exception as e:
            logger.error(f"Error during prefetch of as-is data: {e}")


    def schedule_daily_prefetch(self, countries_and_langs: List[tuple], 
                                hour: int = 0, minute: int = 0, second: int = 0) -> None:
        logger.debug(f'now: {datetime.now()}')
        if Stock_Services._scheduler_thread is not None:
            logger.error("Scheduler is already running")
            return
        
        def _prefetch_job():
            logger.debug(f"Starting scheduled prefetch at {datetime.now()}")
            if self.world_stores is not None:
                logger.debug(f'Clearing out old world store daily cache - Cleared {len(self.world_stores)} countries')
            self.world_stores = None
            self._prefetch_as_is_data(countries_and_langs)
            logger.debug(f"Completed scheduled prefetch at {datetime.now()}")

        schedule.every().day.at(f"{hour:02d}:{minute:02d}:{second:02d}").do(_prefetch_job)
        # schedule.every(1).minutes.do(prefetch_job)  # Run every 5 minutes

        # Run the scheduler in a separate thread to not block main program
        def _run_scheduler():
            while True:
                schedule.run_pending()
                logger.debug('scheduler begin checking every hour')
                time.sleep(3600)

        # Create and start the scheduler thread
        Stock_Services._scheduler_thread = threading.Thread(target=_run_scheduler, daemon=True)
        Stock_Services._scheduler_thread.start()