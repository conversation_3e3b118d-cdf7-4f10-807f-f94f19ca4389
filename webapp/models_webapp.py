from pydantic import BaseModel
from typing import List, Any, Optional

class SearchResult(BaseModel):
    id: int
    name: str
    name_w_type: str
    thumbnail: str
    stag: str
    product_num: str
    product_type: str


class Pdf(BaseModel):
    file_name: str
    pdf_path: str
    page_count: int
    parts: List[str]


class Product(BaseModel):
    name: str
    name_w_type: str
    image_dir: str
    product_num: str
    details: List[str]
    measurements: Optional[Any]
    product_type: str
    pdfs: List[Pdf]