import os
from datetime import datetime
from pymongo import MongoClient
from bson.objectid import ObjectId
from pymongo.collection import Collection

class QuestionAnswerServices:
    _CONN_STRING_ = os.getenv("MONGO_CONNECTION_STRING")
    _DB_NAME_ = 'manualdb'
    _COLLECTION_NAME_ = 'furniture_questions'
    _FURNITURE_ID_ = 'furniture_id'

    def __init__(self):
        self.client = MongoClient(self._CONN_STRING_)
        self.questions_collection = self.set_up_db()

    def set_up_db(self) -> Collection:
        mongo_db = self.client[self._DB_NAME_]
        questions_collection = mongo_db[self._COLLECTION_NAME_]
        questions_collection.create_index(self._FURNITURE_ID_, unique=True)
        return questions_collection

    def get_questions(self, furniture_id: str):
        furniture = self.questions_collection.find_one({self._FURNITURE_ID_: furniture_id})
        return furniture['questions'] if furniture and 'questions' in furniture else None

    def handle_post_time(self, post_time:datetime=None):
        time_format = '%b %d, %Y'
        if post_time is None:
            return datetime.now().strftime(time_format)
        return post_time.strftime(time_format)

    def insert_question(self, furniture_id: str, user: str, text: str, email: str, post_time:datetime=None) -> None:
        post_time = self.handle_post_time(post_time)
        question_id = str(ObjectId())
        question = {
            'question_id': question_id,
            'user': user,
            'email': email,
            'text': text,
            'posted_at': post_time,
            'answers': []
        }

        self.questions_collection.update_one(
            {self._FURNITURE_ID_: furniture_id},
            {'$push': {'questions': question}},
            upsert=True
        )
        return question_id

    def insert_answer(self, furniture_id: str, question_id: str, user: str, text: str, email: str, post_time=None) -> None:
        post_time = self.handle_post_time(post_time)
        answer_id = str(ObjectId())
        answer = {
            'answer_id': answer_id,
            'user': user,
            'email': email,
            'text': text,
            'posted_at': post_time
        }

        self.questions_collection.update_one(
            {
                self._FURNITURE_ID_: furniture_id,
                'questions.question_id': question_id
            },
            {
                '$push': {
                    'questions.$.answers': answer
                }
            }, 
            upsert=True
        )