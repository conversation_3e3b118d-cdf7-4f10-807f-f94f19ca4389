from sqlalchemy import text
from unidecode import unidecode
from flask import url_for

import re, os, requests, logging
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta

from utils import parse_search_results
from static_constant import ANUM_SEARCH, PART_SEARCH, TYPE_SEARCH

logger = logging.getLogger('webapp')

class SearchServices:
    def __init__(self, db):
        self.db = db
        self.all_models_sorted = None
        self._part_cache = {}
        self._cache_timestamp = datetime.now()
        self._cache_ttl = timedelta(hours=6)


    def get_all_models_sorted(self) -> list:
        if self.all_models_sorted is None:
            models = self.get_all_models()
            model_set = set()
            for model in models:
                names = model[0].split('/')
                for name in names:
                    # convert to closest ASCII equivalents. Ex: "bestå" to "besta"
                    converted = unidecode(name.strip())
                    model_set.add(converted)
            self.all_models_sorted = sorted(model_set)
        return self.all_models_sorted


    def get_search_results(self, search_term, search_type, page, per_page):
        if search_term is None or search_term == '':
            return [], 0
        
        search_term = search_term.strip()
        search_results = [] , 0
        
        if search_type == PART_SEARCH:
            search_results = self.search_furniture_by_part(search_term, page, per_page)
        elif search_type == ANUM_SEARCH:
            search_results = self.search_furniture_by_article_num(search_term, page, per_page)
        elif search_type == TYPE_SEARCH:
            search_results = self.search_furniture_by_type(search_term, page, per_page)
        
        return search_results
    

    def search_details_by_fur_id(self, fur_id):
        try:
            fur_id = int(fur_id)
        except ValueError:
            return []
        
        query = """
            SELECT 
                Furniture.name, Furniture.name_w_type, 
                PdfFile.file_path AS pdf_file_path, 
                Furniture.image_dir , Furniture.product_num, Furniture.details, 
                Furniture.measurements, Furniture.product_type,
                PdfFile.page_count AS page_count, 
                GROUP_CONCAT(DISTINCT Part.part_number SEPARATOR ', ') AS part_numbers,
                PdfFile.file_name AS pdf_file_name
            FROM 
                Furniture
            INNER JOIN 
                FurniturePdf ON Furniture.id = FurniturePdf.furniture_id
            INNER JOIN 
                PdfFile ON FurniturePdf.pdf_file_id = PdfFile.id
            LEFT JOIN 
                PdfPart ON PdfFile.id = PdfPart.pdf_file_id
            LEFT JOIN 
                Part ON PdfPart.part_id = Part.id
            WHERE 
                Furniture.id = :fur_id
            GROUP BY 
                Furniture.name, PdfFile.file_path, PdfFile.page_count, PdfFile.file_name;
        """
        result = self.db.session.execute(
            text(query),
            {"fur_id": fur_id}
        )
        return result.fetchall()


    def search_furniture_by_article_num(self, product_num, page, per_page):
        product_num = product_num.replace(".", "")
        product_num = product_num[:10]
        offset = (page - 1) * per_page
        total = self.db.session.execute(
            text("""
                 SELECT COUNT(*)
                 FROM Furniture 
                 WHERE product_num = :product_num
                 """),
            {"product_num": product_num}
        ).scalar()
        result = self.db.session.execute(
            text("""
                 SELECT id, name, name_w_type, image_dir, product_num, product_type
                 FROM Furniture 
                 WHERE product_num = :product_num
                 LIMIT :per_page OFFSET :offset
                 """),
            {"product_num": product_num, "per_page": per_page, "offset": offset}
        )
        return result.fetchall(), total
    

    def search_furniture_by_part(self, part_num, page, per_page):
        query = """
            SELECT Furniture.id, Furniture.name, Furniture.name_w_type, Furniture.image_dir,
            Furniture.product_num, Furniture.product_type
            FROM Furniture
            JOIN FurniturePdf ON Furniture.id = FurniturePdf.furniture_id
            JOIN PdfFile ON FurniturePdf.pdf_file_id = PdfFile.id
            JOIN PdfPart ON PdfFile.id = PdfPart.pdf_file_id
            JOIN Part ON PdfPart.part_id = Part.id
            WHERE Part.part_number = :part_num
            LIMIT :per_page OFFSET :offset;
        """
        count_query = """
            SELECT COUNT(*)
            FROM Furniture
            JOIN FurniturePdf ON Furniture.id = FurniturePdf.furniture_id
            JOIN PdfFile ON FurniturePdf.pdf_file_id = PdfFile.id
            JOIN PdfPart ON PdfFile.id = PdfPart.pdf_file_id
            JOIN Part ON PdfPart.part_id = Part.id
            WHERE Part.part_number = :part_num;
        """
        part_num = part_num[:10]
        offset = (page - 1) * per_page
        total = self.db.session.execute(
            text(count_query),
            {"part_num": part_num}
        ).scalar()
        result = self.db.session.execute(
            text(query),
            {"part_num": part_num, "per_page": per_page, "offset": offset}
        )
        return result.fetchall(), total
    

    def search_furniture_by_name(self, fur_name, page, per_page):
        fur_name = fur_name[:26].lower()
        fur_name = fur_name.replace("ikea", "").strip()
        offset = (page - 1) * per_page
        total = self.db.session.execute(
            text("""
                 SELECT COUNT(*)
                 FROM Furniture 
                 WHERE name LIKE :fur_name
                 """),
            {"fur_name": "%" + fur_name + "%"}
        ).scalar()
        result = self.db.session.execute(
            text("""
                 SELECT id, name, name_w_type, image_dir, product_num, product_type
                 FROM Furniture 
                 WHERE name LIKE :fur_name
                 LIMIT :per_page OFFSET :offset
                 """),
            {"fur_name": "%" + fur_name + "%", "per_page": per_page, "offset": offset}
        )
        return result.fetchall(), total


    def get_all_models(self):
        result = self.db.session.execute(
            text("""
                SELECT name, COUNT(*)
                FROM Furniture 
                GROUP BY name
                ORDER BY name ASC
                """)
        )
        return result.fetchall()
    
    def get_models(self, fur_name):
        result = self.db.session.execute(
            text("""
                 SELECT id, name, name_w_type, image_dir, product_num, product_type
                 FROM Furniture 
                 WHERE name LIKE :fur_name
                 """),
            {"fur_name": "%" + fur_name + "%"}
        )
        return result.fetchall()

    def search_furniture_by_type_old(self, fur_type, page, per_page):
        """ 
        Not flexible enough for search by type. 
        For example: uppland sofa cover will no be found. 
                    It has to be uppland cover for sofa (exact order of words)
        """
        fur_type = fur_type[:26]
        offset = (page - 1) * per_page
        total = self.db.session.execute(
            text("""
                SELECT COUNT(*)
                FROM Furniture 
                WHERE name_w_type LIKE :fur_type
                """),
            {"fur_type": "%" + fur_type + "%"}
        ).scalar()
        result = self.db.session.execute(
            text("""
                SELECT id, name, name_w_type, image_dir, product_num, product_type
                FROM Furniture 
                WHERE name_w_type LIKE :fur_type
                LIMIT :per_page OFFSET :offset
                """),
            {"fur_type": "%" + fur_type + "%", "per_page": per_page, "offset": offset}
        )
        return result.fetchall(), total
    

    def get_total_counts(self):
        # Query to count total number of products
        product_count_query = text("SELECT COUNT(*) FROM Furniture")
        product_count = self.db.session.execute(product_count_query).scalar()

        # Query to count total number of parts
        part_count_query = text("SELECT COUNT(*) FROM Part")
        part_count = self.db.session.execute(part_count_query).scalar()

        return {
            "total_products": product_count + 6000,
            "total_parts": part_count
        }


    def sanitize_input(self, input_string):
        pattern = "[\+\-\*\"~\(\)>\<@/]"
        return re.sub(pattern, ' ', input_string)
    
    def search_furniture_by_type(self, fur_type, page, per_page):
        stop_words = {
            'a', 'about', 'an', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'how', 'i', 'in', 'is', 'with', 'the',
            'it', 'of', 'on', 'or', 'that', 'the', 'this', 'to', 'was', 'what', 'when', 'where', 'who', 'will', 
        }
        fur_type = self.sanitize_input(fur_type[:60].lower())
        fur_type = " ".join(f'+{word}' for word in fur_type.split() if word not in stop_words)
    
        offset = (page - 1) * per_page
        total = self.db.session.execute(
            text("""
                SELECT COUNT(*)
                FROM Furniture 
                WHERE MATCH(name_w_type) AGAINST(:fur_type IN BOOLEAN MODE)
                """),
            {"fur_type": fur_type}
        ).scalar()
        result = self.db.session.execute(
            text("""
                SELECT id, name, name_w_type, image_dir, product_num, product_type
                FROM Furniture 
                WHERE MATCH(name_w_type) AGAINST(:fur_type IN BOOLEAN MODE)
                LIMIT :per_page OFFSET :offset
                """),
            {"fur_type": fur_type, "per_page": per_page, "offset": offset}
        )
        return result.fetchall(), total
    

    def search_related_products(self, fur_type):
        """
        Retrieves related products based on the given fur type.
        Fur type is the first word of the furniture type.

        """
        first_word = fur_type.split()[0]
        query = text("""
            SELECT id, name, name_w_type, image_dir, product_num, product_type
            FROM Furniture 
            WHERE name_w_type LIKE :fur_type
            ORDER BY RAND()
            LIMIT 6
        """)
        results = self.db.session.execute(query, {"fur_type": "%" + first_word + "%"}).fetchall()
        return results
    

    def _fetch_part_json(self, part_number: str):
        url = f'https://api.prod.apo.ingka.com/part/{part_number}'
        headers = {
        'accept': 'application/json',
        'accept-language': 'en-us',
        'content-language': 'en-us',
        'content-type': 'application/json',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
        'Cookie': '_abck=DA631FF2D5609E5D153F59DDBD4C1616~-1~YAAQl8pNF4jDiQSTAQAA1xolFAwLmqC8AoOb3eWF0gQwreeH++KMCqXkARu7lsfpciYGGNh5122rgVqeJ6N9md6DAvA+C+vgFLJZTIUUJhQYBS10Md43gQ2q8QHME5EJ92mCqf3KpzZhhszp1s9PySzo+Gb+j1tJNaz7V/Qkz1wBusG0ODZKBT1VMV6VI6UeOVqf2aKKBnZWQZDzH22J/Ni0/W5V/LfpXh/i9ZqYIs9JFzyPSeyNBVi2XFmkQzvvyPdDvykb3jaMx6g3HpMG9H5u8u9T/zppNDPdLYFivT0tdTpDAz73zMvnpeRPY0Pv1fT68jKC2hh5hZcdes4qmuOFHVrV6KmrhF2/n5O7JTbiDudptW8DWkxhrQ8lJefm9OxjagpYXlwniGvpLC7wF1u7fF8JVlvHzGy8DJ5AVZt/twYKCIybRFCjJXXWr9zVlmxTlgBCiCQFy8+2cy5sc/z33c6ZfbepsQn7/gc/nB0hfDVrw+K6IAJYJz5qbwlm93XzztxderSegByWjrafzlND+yy0LFxX/L4iFHCRzmWjuyfjnBp/zmOeqzr6t8ns3wuiRggGHfOpnlktiISQujPkFweR6FC0buMaOZdgbscaEcK3+LJ/2vU4/IlKyo3+3cxPlHf9lsdlbw==~-1~-1~-1; bm_sz=9F09956884F5EF41691E19AB8360AD8A~YAAQl8pNF4rDiQSTAQAA1xolFBnlinqy8tY5Z9BAIkV5hMLtZdEo4xw6HDftNHhpa+uT8UopFxBQd/SPiAcYV9dL+81FUS7abBVEhBYuM0M5jlxn/ekcJiqiiM0cxoN4y3Ndv4k6fjq4ZNPDKTQazxEHpU1aEaFDQcq5TEz3u7XMnuDGQIuOVKLlmEsNHzJnIz6vWpcj1GjOfy+s7XiKpGain5fu8bvvRBCfOg4b9K3zbXosLe1bN8DjGnrOz1nRBmKjP5BTtUGCwZTul5fEXIT8InyH67b5ooHDNVEm3wAZNHdYvaKDkZDzRECbazRWB7+vJ5gNe5/Y4GKnIh6E2haNGm4Y5JKwW/cqRa/s~3291191~3683382; ak_bmsc=57A672D1DBEDBB45899FF2880261E212~000000000000000000000000000000~YAAQl8pNF4nDiQSTAQAA1xolFBkGWLakbmWhd1jBqpuEJow2E7oxTEhxtal0Iz9eFUB/Pkm0efx1EQaTOMcKtm84zbudWKH5vK+nEES6dzEtWr1b4XAldAPp+89uIzNHj4Poj5RV66fqQ+gGYOL8hyp/Pl0FCszp6rNbZTlfcJJpbBGgmVKTKs6cHbBcFYiD+aLckhCP5ddHixl+fqsJjICJhv6hKzVcD5PUmCtDPAIrguyVfuqB4Oxah+fPzjE5sH2vIR1V04k0gVx9sA3uZnbpMz9XfOwQz1/l9QEqSRvpFUD6I//S8H08yhgaNqX9wNMmxjx+Z1c146svkzbgOiqz6rcsZik/u44gwPsZXpIeskqA; bm_sv=7271991D8A8AD8F292228C42D5D3AE16~YAAQl8pNF4EfjASTAQAA01RCFBlYcMkp7haW9KNk/8nsFN2L1vYp5XB2b+GfszRHG5YJG97ka2wr2C+xyecCPNuThNPDgUHUxD5CYDPkd2M0G5gSL4O7hOvPqjYwTJmXZcRqAfJ+i8MIWgzFzdysks12zoviud7Ss/E1ZiqC+lptXNhRxajXB3nfl9RYQmVdw6zRqGBGPqsG8odRYstA/y08AM7eYTG2oigIGU9JhvTzs650HtsQc5C3I9ccpqWuUH1/GYgOXq0=~1; JSESSIONID=0000BodSQo4azyuxXGrn1Nmo4ZQ:b448d3f7-c5a1-48ec-9174-d5134e1224a8'
        }
        logger.debug(f'Fetching part json for {part_number}')
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting part json for {part_number}: {e}")
            return None  
    

    def _check_cache_expiry(self):
        if datetime.now() - self._cache_timestamp > self._cache_ttl:
            logger.info(f"Cache expired after {self._cache_ttl}. Clearing cache with {len(self._part_cache)} entries.")
            self._part_cache = {}
            self._cache_timestamp = datetime.now()


    def get_part_data(self, part_number: str):
        self._check_cache_expiry()
        # Check if part exists in class-level cache
        if part_number in self._part_cache:
            if self._part_cache[part_number] is None:
                logger.warning(f"Cache hit for invalid part number: {part_number}")
                return None    
            logger.debug(f"Cache hit for part number: {part_number}")
            return self._part_cache[part_number]
            
        # If not in cache, fetch from API
        data = self._fetch_part_json(part_number)
        if data is None or data.get('itemId') is None:
            logger.warning(f"No data found for part number: {part_number}")
            # Cache the None result to avoid future API calls
            self._part_cache[part_number] = None
            return None
            
        img_base_url = "https://www.ikea.com/global/assets/customer-service/returns-claims/spareparts/images"
        part_data = {
            'part_number': part_number,
            'name': data.get('partName'), 
            'description': data.get('partDescription'),
            'img': f"{img_base_url}/{part_number}.png"
        }
        
        # Cache the result
        logger.debug(f"Caching data for part number: {part_number}")
        self._part_cache[part_number] = part_data
        
        return part_data

    
    ### XXX only use in development server then commit to codebase
    def get_sitemap(self) -> None:
        root = ET.Element("urlset")
        root.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
        time_now = datetime.now().strftime("%Y-%m-%d")
        # Add static routes
        static_routes = ['index', 'models', 'about', 'stock', 'asis']
        for route in static_routes:
            url = ET.SubElement(root, "url")
            loc = ET.SubElement(url, "loc")
            loc.text = url_for(route, _external=True)
            lastmod = ET.SubElement(url, "lastmod")
            lastmod.text = time_now

        # Add dynamic routes (model/BESTA)
        models = self.get_all_models()
        model_set = set()
        for model in models:
            names = model[0].split('/')
            for name in names:
                converted = unidecode(name.strip())
                model_set.add(converted)
        model_set = sorted(model_set)

        for model in model_set:
            url = ET.SubElement(root, "url")
            loc = ET.SubElement(url, "loc")
            loc.text = url_for('model', model_name=model, _external=True)
            lastmod = ET.SubElement(url, "lastmod")
            lastmod.text = time_now
            # Add dynamic routes (e.g., detail pages)
            product_models = self.get_models(model)
            products = parse_search_results(product_models)
            for product in products:
                url = ET.SubElement(root, "url")
                loc = ET.SubElement(url, "loc")
                loc.text = url_for('details', id=product.id, stag=product.stag, _external=True)
                lastmod = ET.SubElement(url, "lastmod")
                lastmod.text = time_now

        tree = ET.ElementTree(root)
        sitemap_path = os.path.join('webapp', 'sitemap.xml')
        tree.write(sitemap_path, encoding='utf-8', xml_declaration=True)

        ### Replace sitemap.xml localhost with the actual domain
        with open(sitemap_path, 'r', encoding='utf-8') as file:
            content = file.read()
        new_content = re.sub(r'https?://(127\.0\.0\.1|localhost):5000', 'https://easyrebuild.com', content)
        with open(sitemap_path, 'w', encoding='utf-8') as file:
            file.write(new_content)