# Use an official Python runtime as a parent image
FROM python:3.11.6

# When this is set to 1, Python won't buffer the outputs of your program. 
# This is useful in a Docker container because it allows the logs from your 
# Python application to be displayed in real-time. Without this, 
# Python might buffer the output, meaning you wouldn't see the logs immediately.
ENV PYTHONUNBUFFERED 1

# Create code directory and set it as working directory
RUN mkdir /code
WORKDIR /code

# Copy only requirements.txt first to leverage Docker cache
COPY ./requirements.txt /code/

# Install any needed packages specified in requirements.txt
RUN pip install -r requirements.txt

# Copy the current directory contents (e.g my local codebase) into the container at /code
COPY . /code/

# Create logs directory and set permissions
RUN mkdir -p /code/logs && chmod 755 /code/logs

# Make port 5000 available to the world outside this container
EXPOSE 5000

# for production, use Waitress in app.py
CMD ["python", "app.py"]

# for production, no cache is needed
# RUN pip install --no-cache-dir -r requirements.txt

# for development, just use flask server
# Run app.py when the container launches
# CMD ["python", "app.py"]

# for production, use gunicorn (retired)
# wsgi refers to wsgi.py. It explicitly indicates that this file is the WSGI entry point for the application.
# "app" represents the WSGI application. 
# CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]

