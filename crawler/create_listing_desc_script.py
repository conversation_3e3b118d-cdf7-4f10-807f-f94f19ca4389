import os, requests, shutil
from src.utils_crawler import fetch_product_detail

def extract_text(data, key):
    if key in data:
        if key == 'careInstructions':
            if 'careInstructionTexts' in data[key][0]:
                return [item['careInstructionText'] for item in data[key][0]['careInstructionTexts']]
        else:
            return [item['text'] for item in data[key]]
    else:
        return []

def download_images(pic_list: list, folder_name: str) -> None:
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)

    for i, pic in enumerate(pic_list):
        response = requests.get(pic, stream=True)
        response.raise_for_status()

        with open(os.path.join(folder_name, f'image{i}.jpg'), 'wb') as out_file:
            shutil.copyfileobj(response.raw, out_file)
        
pid = '50348908'
data = fetch_product_detail(pid)
if data is not None:
    data = data['data'][0]
    eng_localise = data['localisedCommunications'][0]
    media_list = eng_localise['media']
    mainMedia = media_list[0]
    itemNo = data['itemKeyGlobal']['itemNo']
    name = eng_localise['productName']
    product_type = eng_localise['productType']['name']
    folder_name = f'{name}-{product_type}-{itemNo}'.replace(' ', '_')

    # details
    keys = ['benefits', 'benefitSummary']
    detail_list = []
    for key in keys:
        try:
            value = eng_localise[key]
            if isinstance(value, list):
                detail_list += value
            elif isinstance(value, str):
                detail_list.append(value)
        except KeyError:
            pass
    detail_string = ''
    if len(detail_list) > 0:
        detail_string = "\nDetails:\n"
        detail_string += '\n'.join(detail_list)
    
    long_benefit_string_list = extract_text(eng_localise, 'longBenefits')
    if len(long_benefit_string_list) > 0:
        detail_string += '\n'.join(long_benefit_string_list)

    # materials
    material_string = ''
    if 'materials' in eng_localise:
        materials = eng_localise['materials'][0]
        if 'partMaterials' in materials:
            materials = materials['partMaterials']
            if 'materialText' in materials[0] and 'partText' in materials[0]:
                material_string = "\n\nMaterial:\n"
                material_string += '\n'.join(f"{m['partText']} {m['materialText']}" for m in materials)
            elif 'materialText' in materials[0]:
                material_string = "\n\nMaterial:\n"
                material_string += '\n'.join(m['materialText'] for m in materials)
    
    # care instructions
    care_instructions_string = ''
    care_instructions_string_list = extract_text(eng_localise, 'careInstructions')
    if len(care_instructions_string_list) > 0:
        care_instructions_string = "\n\nCare Instructions:\n"
        care_instructions_string += '\n'.join(care_instructions_string_list)
    
    # good to know
    good_to_know_string = ''
    good_to_know_string_list = extract_text(eng_localise, 'goodToKnows')
    if len(good_to_know_string_list) > 0:
        good_to_know_string = "\n\nGood to Know:\n"
        good_to_know_string += '\n'.join(good_to_know_string_list)
    
    # measurements
    measurements_string = ''
    if 'measurements' in eng_localise:
        measurements = eng_localise['measurements']['detailedMeasurements']
        measurements_string = "\n\nMeasurements:\n"
        measurements_string += '\n'.join(f"{m['typeName']}: {m['textImperial']} ({m['textMetric']})" for m in measurements)

    # package measurements
    package_measurements_string = ''
    if 'packageMeasurements' in eng_localise:
        package_measurements = eng_localise['packageMeasurements']
        package_measurements_string = "\n\nPackage Measurements:\n"
        package_measurements_string += '\n'.join(f"{m['typeName']}: {m['textImperial']} ({m['textMetric']})" for m in package_measurements)


    fullname = mainMedia['altText']
    mainPic = mainMedia['variants'][4]['href']
    media_list = eng_localise['media']
    pics = []
    for media in media_list:
        if 'VIDEO' in media['typeName']:
            continue
        pics.append(media['variants'][4]['href'])
    
    download_images(pics, folder_name=folder_name)
    description_content = f"""Brand new IKEA {fullname} in original package.
Article number: {'.'.join([itemNo[i:i+3] for i in range(0, len(itemNo), 3)])}
{detail_string}{material_string}{care_instructions_string}{good_to_know_string}{measurements_string}{package_measurements_string}
"""
    desc_file = os.path.join(folder_name, 'description.txt')
    with open(desc_file, 'w') as f:
        f.write(description_content)

print('done')