import os, glob, shutil
from src.utils_crawler import upload_files_to_bunny
from src.pdf_helper import convert_pdf_to_img


def process_pdf_files(pdf_root_dir:str):
    
    pdfs = os.listdir(pdf_root_dir)

    for pdf_folder_name in pdfs:
        # list all files in the dir
        pdf_child_dir = os.path.join(pdf_root_dir, pdf_folder_name)
        files = os.listdir(pdf_child_dir)
        # convert pdf to img
        for file in files:
            # if the file is a pdf
            if file.endswith('.pdf'):
                pdf_file = os.path.join(pdf_child_dir, file)
                # convert to png and save to view folder
                view = 'view'
                convert_pdf_to_img(pdf_file, view, pdf_child_dir)
                break

        view_dir = os.path.join(pdf_child_dir, view)
        # remove all jpg files in the view dir
        for jpg_file in glob.glob(os.path.join(view_dir, '*.jpg')):
            os.remove(jpg_file)


def upload_pdf_images(pdf_root_dir:str):
    pdf_folders = os.listdir(pdf_root_dir)
    for pdf_folder_name in pdf_folders:
        # list all files in the dir
        pdf_dir = os.path.join(pdf_root_dir, pdf_folder_name)
        view_dir = os.path.join(pdf_dir, 'view')
        # upload all pdf images
        upload_files_to_bunny(view_dir)

        # move the pdf_dir to the backup dir
        backup_pdf_dir = os.path.join(f'lib-BACKUP/{pdf_root_dir}', pdf_folder_name)
        shutil.move(pdf_dir, backup_pdf_dir)
        print(f'Moved {pdf_dir} to {backup_pdf_dir} after successful upload.')


def upload_model_files(model_root: str):
    model_folders = os.listdir(model_root)
    for model_folder_name in model_folders:
        model_dir = os.path.join(model_root, model_folder_name)
        upload_files_to_bunny(model_dir)

        # Move the model_dir to the backup_dir after uploading the files
        backup_model_dir = os.path.join(f'lib-BACKUP/{model_root}', model_folder_name)
        shutil.move(model_dir, backup_model_dir)
        print(f'Moved {model_dir} to {backup_model_dir} after successful upload.')


def upload_lib():
    LIB_DIR = 'lib-test'
    PDF_ROOT_DIR = f'{LIB_DIR}/pdf'
    MODEL_ROOT_DIR = f'{LIB_DIR}/model'

    upload_pdf_images(PDF_ROOT_DIR)
    upload_model_files(MODEL_ROOT_DIR)
    print('Done uploading Lib folder to BunnyCDN. Lib folder is now empty.')

if __name__ == '__main__':
    upload_lib()

    print('done')
    
    