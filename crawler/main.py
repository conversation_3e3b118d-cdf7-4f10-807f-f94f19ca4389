import json
from logging import Logger
from time import sleep
from src.product_parser import <PERSON>Parser
from src.utils_crawler import fetch_by_catageory, fetch_part, fetch_product_detail, fetch_last_chance, initialize_logger
from src.data_access import read_list_from_db, write_to_db

def process_product(product_id, logger:Logger):
    sleep(.2)
    data = fetch_product_detail(product_id)
    logger.info(f'***------ fetching data for pid: {product_id} ------***')
    if data is None:
        logger.info('no data for ' + product_id + '... skipping')
        return True
    
    if 'data' not in data:
        logger.info('no "data" key for ' + product_id + '... skipping')
        return True
    
    data = data['data'][0]
    parser = ProductParser(data)

    if parser.should_crawl():
        return parser.execute()
    else :
        logger.info(f'*** pid {product_id} is not eligible for crawling ***')
        return True
            

def process_by_links(last_chance=True):
    logger_ = initialize_logger(logger_name=__name__, filename=f'crawler_{__name__}')
    logger_.info('------------------------- start process by api offset -------------------------')
    
    CRAWLED_TABLE = 'crawled_data' 
    CRAWLED_COLUMN = 'product_url'

    MAX = 999_999
    offset = 0
    size = 24
    logger_.info(f'* fixed offset: {offset} ----- fixed size: {size} *')

    # iterate from offset 0 to total products
    while offset < MAX:
        logger_.info(f'***** Begin with offset: {offset} *****')
        logger_.info('Fetching crawled list from database')
        crawled_list = read_list_from_db(CRAWLED_TABLE, CRAWLED_COLUMN)

        diff_list = []
        to_crawl_list = []
        to_crawl_dict = {}
        logger_.info('Fetching product list from API')

        if last_chance:
            data = fetch_last_chance(offset, size)
        else:
            data = fetch_by_catageory(offset, size)
        
        results = data['results']
        if len(results) == 0:
            logger_.info('No results... exiting')
            break

        items = results[0]['items']
        for item in items:
            if 'product' not in item: # random unparsable item, skip...
                continue
            product = item['product']
            url = product['pipUrl']
            item_no = product['itemNoGlobal']
            to_crawl_list.append(url)
            to_crawl_dict[url] = item_no

        # compare the 2 lists and return the difference
        diff_list = list(set(to_crawl_list) - set(crawled_list))
        logger_.info(f'Diff list: {diff_list}')
        offset += size
        sleep(6)
        
        if len(diff_list) > 0:
            # iterate diff list to fetch data for each product id
            for url in diff_list:
                # get the productid from the dict
                item_no = to_crawl_dict[url]
                
                is_finished_without_error = process_product(item_no, logger_)
                if is_finished_without_error:
                    logger_.info(f'Writing {url} to crawled table')
                    write_to_db(CRAWLED_TABLE, CRAWLED_COLUMN, url)
        
        logger_.info('------ end 1 while iteration ------')
                
    logger_.info('------------------------- end -------------------------')
    print('done')


def process_by_pid(pid_list:list):
    logger_ = initialize_logger(logger_name=__name__, filename=f'crawler_{__name__}')
    logger_.info('------------------------- start process by anum list -------------------------')
    
    CRAWLED_TABLE = 'crawled_data' 
    CRAWLED_COLUMN = 'product_url'

    logger_.info('Fetching crawled list from database')
    crawled_list = read_list_from_db(CRAWLED_TABLE, CRAWLED_COLUMN)

    diff_list = []
    to_crawl_list = pid_list
    logger_.info('Fetching anum list from JSON file')

    # compare the 2 lists and return the difference
    diff_list = list(set(to_crawl_list) - set(crawled_list))
    logger_.info(f'Diff list: {diff_list}')

    if len(diff_list) > 0:
        for anum in diff_list:
            is_finished_without_error = process_product(anum, logger_)
            if is_finished_without_error:
                logger_.info(f'Writing anum: {anum} to crawled table')
                write_to_db(CRAWLED_TABLE, CRAWLED_COLUMN, anum)
    
    logger_.info('------ end 1 while iteration ------')
                
    logger_.info('------------------------- end -------------------------')


if __name__ == "__main__":
    part = fetch_part('153312')

    # process_by_pid(['59534878'])
    process_by_links(last_chance=True)
    
    print('done')

