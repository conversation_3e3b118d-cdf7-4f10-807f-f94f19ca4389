import os
import mysql.connector
from dotenv import load_dotenv
from contextlib import contextmanager

from model.product_model import Product, Pdf
from model.error_handler import ErrorLevel, MyException


@contextmanager
def create_connection():
    load_dotenv()

    conn = mysql.connector.connect(
        user=os.getenv("USER"),
        database=os.getenv("DATABASE"),
        host=os.getenv("C_HOST"),
        port=os.getenv("C_PORT"),
        password=os.getenv("C_PASSWORD")
    )
    try:
        yield conn
    finally:
        conn.close()


def insert_product_data(product:Product):
    with create_connection() as conn:
        try: 
            execute_transaction(conn, product)
        except MyException as e:
            raise e


def execute_transaction(conn, product:Product):
    cursor = conn.cursor()
    try:
        cursor.execute("""
            INSERT INTO Furniture (name, name_w_type, details, product_num, image_dir, measurements, product_type)
            VALUES (%s, %s, %s, %s, %s, %s, %s);
        """, (product.name, product.name_w_type, product.details, product.product_num, 
              product.image_dir, product.measurements, product.product_type))

        furniture_id = cursor.lastrowid

        for pdf in product.pdfs:
            if not isinstance(pdf, Pdf):
                raise TypeError(f'pdf is not of type Pdf. pdf: {pdf}')

            # insert pdf if not exists; if exists, get the id
            cursor.execute("""
                    INSERT IGNORE INTO PdfFile (file_name, file_path, page_count)
                    VALUES (%s, %s, %s);
                """, (pdf.file_name, pdf.file_path, pdf.page_count))
            pdf_file_id = cursor.lastrowid
            if pdf_file_id is None or pdf_file_id == 0:
                cursor.execute("""
                    SELECT id FROM PdfFile WHERE file_name = %s;
                """, (pdf.file_name,))
                pdf_file_id = cursor.fetchone()[0]

            cursor.execute("""
                INSERT IGNORE INTO FurniturePdf (furniture_id, pdf_file_id)
                VALUES (%s, %s);
            """, (furniture_id, pdf_file_id))

            for part in pdf.parts:
                # insert part if not exists; if exists, get the id
                cursor.execute("""
                    INSERT IGNORE INTO Part (part_number)
                    VALUES (%s);
                """, (part,))
                part_id = cursor.lastrowid
                if part_id is None or part_id == 0:
                    cursor.execute("""
                        SELECT id FROM Part WHERE part_number = %s;
                    """, (part,))
                    part_id = cursor.fetchone()[0]

                cursor.execute("""
                    INSERT IGNORE INTO PdfPart (pdf_file_id, part_id)
                    VALUES (%s, %s);
                """, (pdf_file_id, part_id))

        conn.commit()
    except Exception as e:
        conn.rollback()
        raise MyException("Error performing Transactional Insertion to database!" + e, ErrorLevel.MAJOR)
    finally:
        cursor.close()


def check_if_pdf_exists(pdf_file_with_aaid:str) -> str:
    if '.pdf' not in pdf_file_with_aaid:
        raise MyException(f"{pdf_file_with_aaid = } does not contain .pdf", ErrorLevel.MAJOR)
    aaid:str = pdf_file_with_aaid.split('.')[0]

    with create_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, file_name, file_path FROM PdfFile WHERE file_name LIKE %s;
        """, ('%' + aaid + '%',))
        result = cursor.fetchone()
        if result is None:
            return None
        
        if aaid not in result[1] or aaid not in result[2]:
            raise MyException(f"Pdf with aaid: {aaid} does not match both file name and path", ErrorLevel.MAJOR)
        
        return result[0]
            

def retrieve_pdf_parts(pdf_id:str):
    with create_connection() as conn:
        cursor = conn.cursor()
        query = """
            SELECT PdfFile.file_name, PdfFile.file_path, PdfFile.page_count,
            GROUP_CONCAT(DISTINCT Part.part_number SEPARATOR ', ') AS part_numbers
            FROM PdfFile
            LEFT JOIN PdfPart ON PdfFile.id = PdfPart.pdf_file_id
            LEFT JOIN Part ON PdfPart.part_id = Part.id
            WHERE PdfFile.id = %s
            GROUP BY PdfFile.id;
        """
        cursor.execute(query, (pdf_id,))
        result = cursor.fetchall()[0]
        return result


def insert_many_into_table(conn, table_name, column_name, data_list):
    for item in data_list:
        insert_into_table(conn, table_name, column_name, item)

def insert_into_table(conn, table_name, column_name, item):
    cur = conn.cursor()
    query = f"INSERT INTO {table_name} ({column_name}) VALUES (%s)"
    cur.execute(query, (item,))
    conn.commit()

def write_to_db(table_name, column_name, item):
    with create_connection() as conn:
        insert_into_table(conn, table_name, column_name, item)

def read_list_from_db(table_name, column_name):
    output = []
    with create_connection() as conn:
        cur = conn.cursor()
        query = f"SELECT {column_name} FROM {table_name}"
        cur.execute(query)
        output = [row[0] for row in cur.fetchall()]
    return output

def write_list_to_db(table_name, column_name, data_list):
    with create_connection() as conn:
        insert_many_into_table(conn, table_name, column_name, data_list)

def insert_part_to_pdf(part_number, pdf_file_name):
    with create_connection() as conn:
        cursor = conn.cursor()

        cursor.execute("""
            INSERT IGNORE INTO Part (part_number)
            VALUES (%s);
        """, (part_number,))

        cursor.execute("""
            SELECT id FROM Part WHERE part_number = %s;
        """, (part_number,))
        part_id = cursor.fetchone()[0]
        print(f"Part number: {part_number} -- id: {part_id}")

        cursor.execute("""
            SELECT id FROM PdfFile WHERE file_name = %s;
        """, (pdf_file_name,))
        pdf_file_id = cursor.fetchone()[0]
        print(f"Pdf file id: {pdf_file_id}")

        cursor.execute("""
            INSERT INTO PdfPart (pdf_file_id, part_id)
            VALUES (%s, %s);
        """, (pdf_file_id, part_id))

        if cursor.rowcount:
            print(f"### Inserted PdfPart record successfully. Number of rows affected: {cursor.rowcount}")
            if cursor.rowcount > 1:
                raise Exception(f"### WARNING: Number of rows affected: {cursor.rowcount}")

        conn.commit()
    
def select_all_pdf_root(pdf_root:str):
    with create_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT * FROM PdfFile
            WHERE file_name LIKE %s;
        """, (f'%{pdf_root}%',))
        result = cursor.fetchall()
        return result

def manual_insert_parts_to_pdfs(parts, pdf_root):
    results = select_all_pdf_root(pdf_root)
    print(f'pdf_root: {pdf_root} has {len(results)} pdfs.')
    file_names = []
    for result in results:
        FILE_NAME_IX = 1
        file_name = result[FILE_NAME_IX]
        print(f'file_name: {file_name}')
        file_names.append(file_name)

    for file_name in file_names:
        for part in parts:
            try:
                insert_part_to_pdf(part, file_name)
            except Exception as e:
                print(f'ERROR inserting: {e}')

########################
##### utility functions to clean up; meant to be run on playground #####
from mysql.connector import Error
def delete_product(furniture_id):
    with create_connection() as conn:
        cursor = conn.cursor()
        try:
            # Start a transaction
            conn.start_transaction()

            # Get the furniture_id
            cursor.execute("SELECT id FROM Furniture WHERE id = %s", (furniture_id,))
            result = cursor.fetchone()
            if not result:
                print(f"No furniture found with product number: {furniture_id}")
                conn.rollback()
                return

            furniture_id = result[0]

            # Remove entries from FurniturePdf
            cursor.execute("DELETE FROM FurniturePdf WHERE furniture_id = %s", (furniture_id,))

            # Get associated PDF file IDs
            cursor.execute("SELECT pdf_file_id FROM FurniturePdf WHERE furniture_id = %s", (furniture_id,))
            pdf_file_ids = [row[0] for row in cursor.fetchall()]

            # Remove entries from PdfPart for each associated PDF
            for pdf_file_id in pdf_file_ids:
                cursor.execute("DELETE FROM PdfPart WHERE pdf_file_id = %s", (pdf_file_id,))

            # Remove entries from PdfFile
            for pdf_file_id in pdf_file_ids:
                cursor.execute("DELETE FROM PdfFile WHERE id = %s", (pdf_file_id,))

            # Finally, remove the furniture record
            cursor.execute("DELETE FROM Furniture WHERE id = %s", (furniture_id,))

            # Commit the transaction
            conn.commit()
            print(f"Successfully removed furniture with product number: {furniture_id}")

        except Error as e:
            print(f"Error: {e}")
            conn.rollback()
        finally:
            if cursor:
                cursor.close()


def check_product_num_exists(product_num):
    with create_connection() as conn:
        cursor = conn.cursor()
        query = "SELECT EXISTS(SELECT 1 FROM Furniture WHERE product_num = %s)"
        cursor.execute(query, (product_num,))
        result = cursor.fetchone()[0]
        return result == 1


def delete_crawled_url(product_num):
    with create_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SET SQL_SAFE_UPDATES = 0;")
        query = "DELETE FROM crawled_data WHERE product_url LIKE %s"
        cursor.execute(query, ('%' + product_num + '%',))
        cursor.execute("SET SQL_SAFE_UPDATES = 1;")
        conn.commit()
##### end of utility functions #####