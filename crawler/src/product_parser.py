import json, re, logging
from pathlib import Path
from src.data_access import check_if_pdf_exists, insert_product_data, retrieve_pdf_parts, check_product_num_exists
from model.product_model import Product, Pdf
from model.error_handler import <PERSON><PERSON><PERSON><PERSON>eve<PERSON>, MyException
from src.pdf_helper import JPG_EXTENSION, PNG_EXTENSION, convert_pdf_to_img, extract_part_numbers_from_pdf_pymupdf, read_part_number_from_images
from src.utils_crawler import download_file, is_dir_empty, list_all_files, upload_files_to_bunny


class ProductParser:
    ENGLISH_LOCALISATION_IX = 0 # index of english localisation
    LOCALISED_COMMUNICATIONS_KEY = 'localisedCommunications'
    ATTACHMENTS_KEY = 'attachments'
    CHILD_ITEM_KEY = 'childItems'
    PDF_VIEW_FOLDER_NAME = 'view'

    def __init__(self, data, for_test=False):
        # Initialize logger
        self.logger = logging.getLogger('__main__')
        self.logger.info('Initializing ProductParser')
        
        self.productData = data

        # internal use for parsing
        ## directory paths
        self.root = None # should be 'lib'
        self.model_root = None # should be 'model'
        self.pdf_root = None # should be 'pdf'

        self.attachments = set() # scraped links to download assembly instructions
        self.thumbPic = None
        self.mainPic = None

        # for mapping to Product model
        self.name = None
        self.name_w_type = None
        self.details = []
        self.itemNo = None
        self.measurements = None
        self.prod_dir = None # should be used to store images of the product
        self.product_type = None
        self.pdfs = []

        # for unit test only
        self.is_pymupdf_finished = False
        self.is_ocr_finished = False
        self.pdf_skipped_count = 0
        self.upload_skipped_count = 0
        self.for_test = for_test


    def should_crawl(self):
        try:
            self.initial_check()
        except MyException as e:
            self.logger.error(e)
            self.logger.error(e.traceback)
            return False
        
        return len(self.attachments) > 0
    

    def initial_check(self):
        self.logger.info('Initial check before parsing')
        self.logger.info('Checking if product number is already exists in database')
        try:
            itemNo = self.productData['itemKeyGlobal']['itemNo']
        except KeyError:
            raise MyException("KeyError: 'itemNo' or 'itemKeyGlobal' not found in data", level=ErrorLevel.MAJOR)
        
        if check_product_num_exists(itemNo):
            raise MyException(f'Product number already exists in database: {itemNo}', level=ErrorLevel.MAJOR)
        
        self.logger.info('Checking has child items and parsing attachment urls')
        hasChildItems = self.check_has_child_items()
        if(hasChildItems):
            self.attachments = self.parse_all_attachments()
        else:
            self.attachments = self.parse_attachment(self.productData)


    def execute(self) -> bool:
        try:
            self.parse_metadata()
            self.create_folder_structure()
            self.handle_attachments() # download pdfs, convert to images, collect part numbers and create pdf objects
            self.download_images() # download main and thumbnail images
            self.check_all_files_downloaded()
            self.upload_all_files() # should stop execution if failed
            self.insert_to_database()
        except MyException as e:
            self.logger.error(e)
            self.logger.error(e.traceback)
            print(e.traceback)
            return False
        
        self.logger.info(f'Skipped pdfs: {str(self.pdf_skipped_count)} -@- Skipped uploads: {str(self.upload_skipped_count)}')
        return True

    def extract_text(self, data, key):
        if key in data:
            if key == 'careInstructions':
                if 'careInstructionTexts' in data[key][0]:
                    return [item['careInstructionText'] for item in data[key][0]['careInstructionTexts']]
            else:
                return [item['text'] for item in data[key]]
        else:
            return []

    def parse_metadata(self):
        self.logger.info('Begin parsing metadata')
        p_data = self.productData

        eng_localise = self.get_english_localisation(p_data)
        
        try:
            mainMedia = eng_localise['media'][0]
        except Exception as e:
            raise MyException(f"Error parsing mainMedia: {e}. Could be KeyError on IXOutBound", level=ErrorLevel.MAJOR)

        try:
            self.itemNo = p_data['itemKeyGlobal']['itemNo']
        except KeyError:
            raise MyException("KeyError: 'itemNo' or 'itemKeyGlobal' not found in data", level=ErrorLevel.MAJOR)

        try:
            self.name = eng_localise['productName']
        except KeyError:
            raise MyException("KeyError: 'productName' not found in data", level=ErrorLevel.MAJOR)

        try:
            self.product_type = eng_localise['productType']['name']
        except KeyError:
            self.logger.warning("-KeyError: 'productType' not found in data")

        keys = ['benefits', 'benefitSummary']
        detail_list = []
        for key in keys:
            try:
                value = eng_localise[key]
                if isinstance(value, list):
                    detail_list += value
                elif isinstance(value, str):
                    detail_list.append(value)
            except KeyError:
                self.logger.warning(f"-KeyError: '{key}' not found in data")
        if len(detail_list) == 0:
            self.logger.warning(f"-KeyError: '{keys}' not found in data. Empty Details field")
        self.details = detail_list

        # benefits
        long_benefit_string_list = self.extract_text(eng_localise, 'longBenefits')
        if len(long_benefit_string_list) > 0:
            self.details.extend(long_benefit_string_list)
        
        # good to know
        good_to_know_string_list = self.extract_text(eng_localise, 'goodToKnows')
        if len(good_to_know_string_list) > 0:
            self.details.extend(good_to_know_string_list)

        # materials
        if 'materials' in eng_localise:
            materials = eng_localise['materials'][0]
            if 'partMaterials' in materials:
                materials = materials['partMaterials']
                if 'materialText' in materials[0] and 'partText' in materials[0]:
                    self.details.append("Material:")
                    self.details.extend(f"{m['partText']} {m['materialText']}" for m in materials)
                elif 'materialText' in materials[0]:
                    self.details.append("Material:")
                    self.details.extend(m['materialText'] for m in materials)

        # care instructions
        care_instructions_string_list = self.extract_text(eng_localise, 'careInstructions')
        if len(care_instructions_string_list) > 0:
            self.details.append("Care Instructions:")
            self.details.extend(care_instructions_string_list)

        try:
            self.measurements = eng_localise['measurements']['detailedMeasurements']
        except KeyError:
            self.logger.warning("-KeyError: 'measurements' not found in data")
            self.measurements = []

        try:
            self.name_w_type = mainMedia['altText']
        except KeyError:
            raise MyException("KeyError: 'altText' not found in data", level=ErrorLevel.MAJOR)

        try:
            self.thumbPic = mainMedia['variants'][1]['href']
        except Exception as e:
            raise MyException("Error parsing: 'thumnail image' not found in data", level=ErrorLevel.MAJOR)

        try:
            self.mainPic = mainMedia['variants'][3]['href']
        except Exception as e:
            raise MyException("Error parsing: 'main image' not found in data", level=ErrorLevel.MAJOR)


    def handle_attachments(self):
        self.logger.info('Begin handling attachements: download pdfs, convert to images, collect part numbers and create pdf objects')
        self.logger.info(f'Number of attachments: {len(self.attachments)}')
        for attachment in self.attachments:
            self.logger.info(f'-handling attachment: {attachment}')
            file_name = attachment.split('/')[-1] # should be .pdf
            # create folder with file_name
            dir_name = file_name.split('.')[0]
            dir_path = self.pdf_root / dir_name
            dir_path.mkdir(exist_ok=True)
            
            # Check if the view folder exists and is empty because exception may 
            # happened and the folder is created but has no content
            if is_dir_empty(dir_path, self.PDF_VIEW_FOLDER_NAME):
                download_file(file_name, dir_path, attachment)

            # convert pdf to jpg and extract part numbers
            self.create_pdf(dir_path)


    def create_pdf(self, dir):
        # Should have ONLY ONE pdf file
        self.logger.info('-Begin creating pdf object process: convert pdf to jpg, get total pages and extract part numbers')
        pdf_files = list(dir.glob('*.pdf'))
        self.logger.info(f'-Number of pdf files: {len(pdf_files)} -- MUST have ONE pdf file')
        for pdf_file in pdf_files:
            pdf_aaid:str = pdf_file.name.split('__')[1]
            self.logger.info(f'--processing pdf file: {pdf_file} ... checking if {pdf_aaid = } exists in database')
            pdf_id:str = check_if_pdf_exists(pdf_aaid)
            if pdf_id is not None:
                self.logger.warning(f'--pdf file already exists in database: {pdf_aaid = }')
                result = retrieve_pdf_parts(pdf_id)
                self.logger.info(f'--retrieving pdf info and parts from database')
                real_file_name:str = pdf_file.name
                pdf = self.map_pdf(result, real_file_name)
                self.pdf_skipped_count += 1
            else:
                self.logger.info('--converting pdf file to jpg')
                pdf_img_dir = convert_pdf_to_img(pdf_file, self.PDF_VIEW_FOLDER_NAME, dir)
            
                # get page count by counting the number of files in the pdf_img_dir
                self.logger.info('--counting number of pages in pdf file')
                pdf_img_dir = Path(pdf_img_dir)
                page_count:int = len(list(pdf_img_dir.glob(f'*.{PNG_EXTENSION}')))
            
                # extract part numbers from pdf file
                parts_from_pdf:set[str] = extract_part_numbers_from_pdf_pymupdf(pdf_file)
                self.is_pymupdf_finished = True
                self.logger.info(f'--extracted part numbers from pdf file: {len(parts_from_pdf)}')
                if len(parts_from_pdf) == 0:
                    self.logger.warning(f'--@ Pymupdf could not find any part numbers from pdf file')
                parts:list[str] = list(parts_from_pdf)

                # ###### XXX: comment out for debugging because it is slow
                # extract part numbers from pdf images
                parts_from_images:set[str] = read_part_number_from_images(pdf_img_dir, isPro=not self.for_test)
                self.logger.info(f'--extracted part numbers from pdf images: {len(parts_from_images)}')
                
                # Combine the sets
                parts = list(parts_from_images.union(parts_from_pdf))
                self.is_ocr_finished = True
                if len(parts_from_images) != len(parts_from_pdf):
                    self.logger.warning(f'--@ file: {len(parts_from_pdf)} - image: {len(parts_from_images)} - combined: {len(parts)}')
                # ######################### end XXX #########################
                
                # create a new PDF object and add it to the list
                self.logger.info('--creating pdf object')
                pdf = Pdf(
                    file_path=str(pdf_img_dir).replace('\\', '/'),
                    file_name=pdf_file.name, 
                    parts=parts, 
                    page_count=page_count)
                
            self.pdfs.append(pdf)


    def map_pdf(self, data, real_file_name:str) -> Pdf:
        # only need to map file_path, page count and parts
        # because file name is different and only share the __AA id
        file_path_ix = 1
        page_count_ix = 2
        parts_ix = 3

        parts_string = data[parts_ix]
        parts = parts_string.split(', ')  if parts_string else []
        return Pdf(
            file_name=real_file_name,
            file_path=data[file_path_ix].replace('\\', '/'),
            page_count=data[page_count_ix],
            parts=parts
        )


    def download_images(self):
        main_filename = f'main_.{JPG_EXTENSION}'
        thumbnail_filename = f'thumb_.{JPG_EXTENSION}'
        self.logger.info('Downloading main and thumbnail images')
        download_file(main_filename, self.prod_dir, self.mainPic)
        download_file(thumbnail_filename, self.prod_dir, self.thumbPic)


    def check_all_files_downloaded(self):
        img_files = list_all_files(self.prod_dir)
        if len(img_files) != 2:
            self.logger.warning(f'Not all images are downloaded: {self.prod_dir}')  
        
        for pdf in self.pdfs:
            pdf_page_images = list_all_files(pdf.file_path) # all png represent each page in the pdf folder
            if len(pdf_page_images) != pdf.page_count:
                raise MyException(f'PDF file {pdf.file_name} - page count NOT match number of image files - {pdf.page_count} != {len(pdf_page_images)}', 
                                  level=ErrorLevel.MAJOR)


    def insert_to_database(self):
        self.logger.info('Begin process inserting product data to database')
        self.sanitize()
        product = self.create_product()
        try: 
            self.logger.info('-Inserting product data to database')
            insert_product_data(product)
        except MyException as e:
            self.logger.error('Error inserting to MySQL with the following data:\n' + str(product))
            raise e
            

    def sanitize(self):
        self.logger.info('-Sanitizing data by converting to JSON to strings and Path objects to strings')
        # Convert the details list to a JSON-formatted string
        self.details = json.dumps(self.details)
        # Convert Path object to string
        self.prod_dir = str(self.prod_dir).replace('\\', '/')
        # Convert the measurements list to a JSON-formatted string
        self.measurements = json.dumps(self.measurements)


    def create_product(self):
        self.logger.info('-Creating Product object')
        product = Product(name=self.name, 
                          name_w_type=self.name_w_type, 
                          image_dir=self.prod_dir, 
                          product_num=self.itemNo, 
                          details=self.details, 
                          measurements=self.measurements, 
                          product_type=self.product_type, 
                          pdfs=self.pdfs)
        return product
    
    
    def upload_all_files(self): 
        self.logger.info('Begin process uploading product data to bunny')
        self.logger.info('-Uploading model images to bunny')
        upload_files_to_bunny(self.prod_dir)

        self.logger.info('-Uploading pdf images to bunny')
        for pdf in self.pdfs:
            pdf_aaid:str = pdf.file_name.split('__')[1]
            self.logger.info(f'--Checking if pdf file exists in database: {pdf_aaid = }')
            exist = check_if_pdf_exists(pdf_aaid)
            
            if exist is not None:
                self.upload_skipped_count += 1
                continue
            
            self.logger.info(f'--Not exist! Begin uploading pdf file: {pdf.file_name}')
            upload_files_to_bunny(pdf.file_path)


    def check_has_child_items(self):
        try:
            self.productData[self.CHILD_ITEM_KEY]
            return True
        except KeyError:
            self.logger.warning(f'No child items found due to KeyError: {self.CHILD_ITEM_KEY} not found in data')
            return False
        
    
    def check_has_attachments(self, item):
        try:
            self.get_english_localisation(item)[self.ATTACHMENTS_KEY]
            return True
        except MyException as e:
            # propagate error to caller
            raise e
        except Exception as e:
            self.logger.warning(f'No attachments found due to KeyError: {self.ATTACHMENTS_KEY} not found in data')
            return False
    

    def get_english_localisation(self, item):
        try:
            return item[self.LOCALISED_COMMUNICATIONS_KEY][self.ENGLISH_LOCALISATION_IX]
        except Exception as e:
            message = f"Error parsing English Localisation - Exception: {e}. Could be KeyError on IXOutBound"
            # create custom exception and raise it to caller
            raise MyException(message, ErrorLevel.MAJOR)
        

    def parse_attachment(self, item):
        has_attachments = self.check_has_attachments(item)
        output = set()
        if not has_attachments:
            return output

        attachments = self.get_english_localisation(item)[self.ATTACHMENTS_KEY]
        for attachment in attachments:
            try:
                if attachment['type'] == 'ASSEMBLY_INSTRUCTIONS':
                    output.add(attachment['href'])
            except KeyError:
                raise MyException("KeyError: attachment['type'] or ['href'] key NOT found", ErrorLevel.MAJOR)

        return output
    

    def parse_all_attachments(self):
        childItems = self.productData[self.CHILD_ITEM_KEY]
        output = set()
        for item in childItems:
            output.update(self.parse_attachment(item))

        self.logger.info(f'attachment HREF added: {len(output)}')
        return output
    

    def create_folder_structure(self):
        self.logger.info('Begin creating folder structure')
        # get current dir
        current_dir = Path('.')
        # go up one level to project root
        root = current_dir.parent

        if self.for_test:
            root = Path('crawler') / 'test-data'

        # create 'lib' folder at the root
        lib_dir = root / 'lib'
        lib_dir.mkdir(exist_ok=True)

        # create 'model' and 'pdf' folders inside 'lib'
        model_dir = lib_dir / 'model'
        pdf_dir = lib_dir / 'pdf'
        model_dir.mkdir(exist_ok=True)
        pdf_dir.mkdir(exist_ok=True)

        # create folder with prod_folder_name inside 'model'
        prod_folder_name = f"{self.name}-{self.product_type}-{self.itemNo}"
        prod_folder_name = re.sub(r'\W', '-', prod_folder_name)
        prod_dir = model_dir / prod_folder_name
        prod_dir.mkdir(exist_ok=True)

        # set the static paths
        self.root = lib_dir
        self.model_root = model_dir
        self.pdf_root = pdf_dir

        # specific to each product
        self.prod_dir = prod_dir


    def __str__(self):
        return f"""
            -- Product Parser Result --
            {self.itemNo} - {self.name_w_type}
            Number of attachments: {len(self.attachments)}
            """