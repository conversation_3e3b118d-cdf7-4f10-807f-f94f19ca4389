from pathlib import Path
import re, os, json, requests, shutil

from PIL import Image
import fitz
import pypdfium2 as pdfium
from ocrmac import ocrmac
import platform


PNG_EXTENSION = 'png'
JPG_EXTENSION = 'jpg'

def extract_text_using_OCRMAC(image_file):
    return ocrmac.OCR(image_file,framework='livetext').recognize()

def perform_ocr_and_parse_ORCMAC(image_file):
    texts = extract_text_using_OCRMAC(image_file)
    texts = ' '.join(item[0] for item in texts)
    part_numbers = parse_parts_regex(texts)
    return part_numbers

def parse_parts_regex(text_to_parse):
    return re.findall(r'(?<!A-)\b(?!0)\d{6}\b|(?<!A-)\b(?!0)\d{8}\b', text_to_parse)

def remove_cloned_images(directory: Path):
    # Use Path.glob to find all .copy.jpg files in the directory
    for file_path in directory.glob(f'*.copy.{PNG_EXTENSION}'):
        # Use Path.unlink to delete each file
        file_path.unlink()


def perform_ocr_and_parse(image_file, isPro=False):
    text = ocr_space_file(image_file, isPro=False)
    text = json.loads(text)
    text = text['ParsedResults'][0]['ParsedText']
    part_numbers = parse_parts_regex(text)
    return part_numbers

def read_part_number_from_image(image_file:Path, isPro=False) -> set[str]:
    part_numbers_in_rotated = []
    part_numbers_in_original = []

    # Convert the image file path to a string and create a copy of the image file
    image_file_str:str = str(image_file)
    image_copy = shutil.copy(image_file_str, image_file_str.rsplit('.', 1)[0] + f".copy.{PNG_EXTENSION}")
    # Perform OCR on the original image
    # part_numbers_in_original = perform_ocr_and_parse(image_copy, isPro)

    # Open the copied image file
    img = Image.open(image_copy)
    # Rotate the image 270 degrees and expand the output image to fit the rotated image
    img = img.rotate(270, expand=True)
    # Save the rotated image back to the file
    img.save(image_copy)

    # check for OS and to either use OCR MAC or windows service
    # Perform OCR on the rotated image
    system = platform.system()
    if system == 'Darwin':
        part_numbers_in_rotated = perform_ocr_and_parse_ORCMAC(image_copy)
    else:
        part_numbers_in_rotated = perform_ocr_and_parse(image_copy, isPro)
    
    # Delete the copied image file
    os.remove(image_copy)

    # Combine the part numbers found in the original and rotated images
    part_numbers_in_image = part_numbers_in_original + part_numbers_in_rotated
    return set(part_numbers_in_image)

def read_part_number_from_images(image_dir: Path, isPro=False) -> set[str]:
    remove_cloned_images(image_dir)
    image_files = list(image_dir.glob(f'*.{PNG_EXTENSION}'))
    # Sort the files by the number in the file name
    # image_files.sort(key=lambda f: int(f.stem[1:]))

    part_numbers:set = set()
    for image_file in image_files:
        part_numbers_in_image = read_part_number_from_image(image_file, isPro=isPro)
        part_numbers.update(part_numbers_in_image)

    return part_numbers


def ocr_space_file(filename, api_key='K88389079888957', isPro=False):
    """ OCR.space API request with local file.
        Python3.5 - not tested on 2.7
    :param filename: Your file path & name.
    :param overlay: Is OCR.space overlay required in your response.
                    Defaults to False.
    :param api_key: OCR.space API key.
                    Defaults to 'helloworld'.
    :param language: Language code to be used in OCR.
                    List of available language codes can be found on https://ocr.space/OCRAPI
                    Defaults to 'en'.
    :return: Result in JSON format.
    
    IMPORTANT DEBUG: if timeout error occurs, check https://status.ocr.space/ for the status of the OCR FREE API 
    """
    endpoint = 'https://apipro1.ocr.space/parse/image' if isPro else 'https://api.ocr.space/parse/image'    
    api_key = 'GPR893UV47DHX' if isPro else api_key

    payload = {'isOverlayRequired': False,
               'apikey': api_key,
               'language': 'eng', 
               'detectorientation': False, 
               'scale': True,
               'filetype': f'{PNG_EXTENSION}',
               'OCREngine': 2
               }
    
    with open(filename, 'rb') as f:
        r = requests.post(endpoint,
                          files={filename: f},
                          data=payload,
                          )
    return r.content.decode()


def extract_part_numbers_from_pdf_pymupdf(pdf_file_path) -> set[str]:
    part_numbers = set()
    doc = fitz.open(pdf_file_path)

    for page in doc:
        text = page.get_text()
        matches = parse_parts_regex(text)
        part_numbers.update(matches)

    return part_numbers

#-------------------------------------------------------------

def convert_pdf_to_img(pdf_path, pdf_dir_name, destination_folder):
    doc = fitz.open(pdf_path)
    saved_dir = os.path.join(destination_folder, pdf_dir_name)
    os.makedirs(saved_dir, exist_ok=True)

    for j, page in enumerate(doc):
        zoom_x = 1.7  # horizontal zoom
        zomm_y = 1.7  # vertical zoom
        mat = fitz.Matrix(zoom_x, zomm_y)  # zoom factor 2 in each dimension
        pix = page.get_pixmap(matrix=mat)  # use 'mat' instead of the identity matrix

        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        img = img.convert("L")  # convert image to grayscale
        width = int(pix.width * 1)
        height = int(pix.height * 1)
        img = img.resize((width, height), Image.LANCZOS)
        
        # XXX make sure to run test product_parser.handle_attachments because pdf.page_count depends on PNG_EXTENSION
        image_path = os.path.join(saved_dir, f"p{j+1}.{PNG_EXTENSION}")
        img.save(image_path, "PNG", optimize=True)

    return saved_dir


def extract_part_numbers_from_pdf_pypdfium2(pdf_file_path):
    pdf = pdfium.PdfDocument(pdf_file_path)
    part_numbers = set()
    for page in pdf:
        textpage = page.get_textpage()
        text = textpage.get_text_range()
        matches = parse_parts_regex(text)
        part_numbers.update(matches)

    return part_numbers