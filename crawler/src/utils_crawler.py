import csv, requests, os, logging, re, json

from datetime import date
from time import sleep
from pathlib import Path
from selectolax.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>


def initialize_logger(logger_name:str=None, filename=None):
    if logger_name is None:
        logger_name = f'{__name__}'
    
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)
    
    # log to console
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(module)s - %(levelname)s - %(message)s - Line: %(lineno)d - %(asctime)s'))
    logger.addHandler(console_handler)

    # log to file
    if filename is None:
        filename = f'{__name__}'
    
    filename = filename + f'_{date.today()}.log'
    file_handler = logging.FileHandler(filename)
    file_handler.setFormatter(logging.Formatter('%(module)s - %(levelname)s - %(message)s - Line: %(lineno)d - %(asctime)s'))
    logger.addHandler(file_handler)

    return logger


def create_filepath_from_caller(filename):
    # Get the directory of the current script
    dir_path = os.path.dirname(os.path.realpath(__file__))
    # Join the directory path and file name
    file_path = os.path.join(dir_path, filename)
    return file_path


def write_list_to_csv(file_path, data_list):
    with open(file_path, 'w', newline='') as file:
        writer = csv.writer(file)
        for item in data_list:
            writer.writerow([item])


def read_list_from_csv(file_path):
    with open(file_path, 'r') as file:
        reader = csv.reader(file)
        return list(item[0] for item in reader)
    

def fetch_part(part_number):
    url = f"https://api.prod.apo.ingka.com/search/{part_number}"

    headers = {
        'accept': 'application/json',
        'accept-language': 'en-us',
        'content-language': 'en-us',
        'content-type': 'application/json',
        'origin': 'https://www.ikea.com',
        'priority': 'u=1, i',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': '_abck=8A6A24AF928862EECD1C1F3D88BBA0AC~-1~YAAQzyXAF9tQXwCVAQAANBnyCw0yY+VuzIYvrREUQjoyGs6UPxk7MA4AN/Y16+Nu6BZEHP2N5/TY7JR+nUb00bQHgqCaGkJzUVZytQAfcMGZD7Dp1Jwx7YZk22q3CarWUVUVmv0MD5IiByBr21RiI8NOld44Mlg9dEulsr9DOe0TZ1uiSZ9rCL+9fhS3DaJfD81pMtIGSxbuip3kp1jY2LEaaqjos9Odeu1vgTxLsxpYZzOLP2eK0ehFZXjZymZ5SKq3DbzdrfIGgetkyNhVtvx/RMGBU4B7CupINkX6vx14Pv6RXSPnPNUj1kx9ncXcqYkkd7vsWCVrVOgAPgmqimAun1oEj7OsvAbcWHMiTOhaOEKxTwONx0af8PvoqTJZnns/+GbUnUEJ6XXFbFj2+C6fP4TD2z37v0njIg==~-1~-1~-1; JSESSIONID=0000qA1XrelRWGTNAw_U9F_PyKa:0796f280-133c-4646-9d2d-1167fa5262e9; __cf_bm=rXWiC9BtFw9JkcmnSgp9dXlLM5sO5k0MThvQ9rXqJvM-1750898469-*******-nXcL6p_mgVTWwD_BkEL8z3EbxEYChB7otxzK_ZDNRoS8r1ncLJAmtWwr2Y91aq0TBWIJ8KFtxqlNIybddnS77SFXrN7ELwDAnW0ehCwQcJw'
    }

    response = requests.get(url, headers=headers)
    data = response.json()
    return data

def fetch_last_chance(offset, size):
    url = "https://sik.search.blue.cdtapps.com/us/en/search?c=listaf&v=20231027"

    payload = f"{{\"searchParameters\":{{\"input\":\"last_chance\",\"type\":\"SPECIAL\"}},\"zip\":\"77539\",\"store\":\"379\",\"isUserLoggedIn\":false,\"partyUId\":null,\"components\":[{{\"component\":\"PRIMARY_AREA\",\"columns\":2,\"types\":{{\"main\":\"PRODUCT\",\"breakouts\":[\"PLANNER\",\"LOGIN_REMINDER\"]}},\"filterConfig\":{{\"subcategories-style\":\"tree-navigation\",\"max-num-filters\":5}},\"sort\":\"RELEVANCE\",\"window\":{{\"offset\":{offset},\"size\":{size}}}}}]}}"
    headers = {
    'Content-Type': 'text/plain'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    data = response.json()
    return data


def fetch_best_sellers(offset, size):
    url = "https://sik.search.blue.cdtapps.com/us/en/search?c=listaf&v=20240110"

    payload = f"""{{
        "searchParameters": {{"input": "top_seller", "type": "SPECIAL"}},
        "zip": "77089",
        "store": "379",
        "isUserLoggedIn": false,
        "optimizely": {{"listing_1985_mattress_guide": null, "listing_fe_null_test_12122023": null, "listing_1870_pagination_for_product_grid": null}},
        "components": [{{
            "component": "PRIMARY_AREA",
            "columns": 3,
            "types": {{"main": "PRODUCT", "breakouts": ["PLANNER", "LOGIN_REMINDER"]}},
            "filterConfig": {{"subcategories-style": "tree-navigation", "max-num-filters": 3}},
            "sort": "RELEVANCE",
            "window": {{"size": {size}, "offset": {offset}}},
            "forceFilterCalculation": true
        }}]
    }}"""
    headers = {
        'authority': 'sik.search.blue.cdtapps.com',
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'text/plain;charset=UTF-8',
        'origin': 'https://www.ikea.com',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    data = response.json()
    return data

def fetch_by_catageory(offset, size):
    import requests

    url = "https://sik.search.blue.cdtapps.com/us/en/search?c=listaf&v=20240110"

    payload = f"""{{
        "searchParameters": {{"input": "st003", "type": "CATEGORY"}},
        "zip": "77581",
        "store": "379",
        "optimizely": {{"listing_1985_mattress_guide": null, "listing_fe_null_test_12122023": null, "listing_1870_pagination_for_product_grid": null, "listing_2527_nlp_anchor_links": null}},
        "isUserLoggedIn": false,
        "components": [{{
            "component": "PRIMARY_AREA",
            "columns": 3,
            "types": {{"main": "PRODUCT", "breakouts": ["PLANNER", "LOGIN_REMINDER"]}},
            "filterConfig": {{"max-num-filters": 3}},
            "sort": "RELEVANCE",
            "window": {{"offset": {offset}, "size": {size}}}
        }}]
    }}"""
    headers = {
        'authority': 'sik.search.blue.cdtapps.com',
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'text/plain;charset=UTF-8',
        'origin': 'https://www.ikea.com',
        'referer': 'https://www.ikea.com/',
        'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    data = response.json()
    return data



def fetch_product_detail(productId):
    # productId = "90301621"
    url = f"https://api.ingka.ikea.com/salesitem/communications/ru/us?itemNos={productId}&expand=childItems"

    payload = {}
    headers = {
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Referer': 'https://www.ikea.com/',
        'x-client-id': '0b0cb44f-194c-42eb-a996-4cc165bd902a',
        'sec-ch-ua-mobile': '?0',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'sec-ch-ua-platform': '"Windows"'
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    data = response.json()
    return data


def download_file(file_name, destination_folder, download_link):
    # create the path where the file will be saved
    file_path = destination_folder / file_name

    # download the file with retries
    for i in range(3):
        response = requests.get(download_link)
        if response.ok:
            break  # exit the loop if the download was successful
        sleep(2 ** i)  # wait for 2^i seconds before the next retry

    # check if the download was successful
    if response.ok:
        # save the file
        with open(file_path, 'wb') as file:
            file.write(response.content)
        return file_path
    else:
        print(f"Failed to download {download_link} after 3 attempts")
        return None
  

def is_dir_empty(dir_path, dir_name):
    for dirpath, dirnames, filenames in os.walk(dir_path):
        if dir_name in dirnames:
            view_path = os.path.join(dirpath, dir_name)
            if os.listdir(view_path):
                return False
    return True


def list_all_files(dir):
    """
    dir.glob('**/*'):
    The ** is a special pattern that matches any files and zero or more directories, 
    subdirectories and symbolic links. 
    The * is a wildcard that matches any sequence of characters, 
    so **/* matches any file or directory at any level of the directory tree.
    """
    dir = Path(dir)
    files = [f for f in dir.glob('**/*') if f.is_file()]
    return files


def upload_files_to_bunny(dir:str, region=''):
    base_url = "storage.bunnycdn.com"
    bucket = os.environ['BUCKET_NAME']
    if region:
        base_url = f"{region}.{base_url}"

    files = list_all_files(dir)
    print(f'-- Uploading {dir} to BunnyCDN bucket: {bucket} --')
    for file in files:
        api_key = os.environ['BUNNY_API_KEY']
        url = f"https://{base_url}/{bucket}/{file.as_posix()}"
        headers = {
            "AccessKey": api_key,
            "Content-Type": "application/octet-stream",
            "accept": "application/json"
        }

        with open(file, 'rb') as file_data:
            response = requests.put(url, headers=headers, data=file_data)
            if response.status_code == 201:
                print(f'--- Uploaded: {file} --- URL: {url} --- BUCKET: {bucket}')
            else:
                raise Exception(f'Error Uploading: {response.status_code} -- {response.text}')
            

def request_ebay(url)->str:
    headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    response = requests.request("GET", url, headers=headers)
    return response.text


def get_ebay_listings_from_username(username:str, max_page:int, keyword:str)->list:
    hrefs = set()
    for page in range(1, max_page):
        url = f'https://www.ebay.com/sch/i.html?_ssn={username}&_ipg=240&_pgn={page}&_nkw={keyword}'
        data = request_ebay(url)
        html = HTMLParser(data)
        a_tags = html.css('a[href*="ebay.com/itm/"]')

        for a in a_tags:
            href = a.attributes['href']
            print(href)
            first_part = href.split('?')[0]
            if 'ebay.com/itm/123456' in first_part:
                continue
            hrefs.add(first_part)
        sleep(3)
    return list(hrefs)


def extract_anum_from_listing_url(url:str, output_dict:dict):
    pattern = r'\b\d{3}\.\d{3}\.\d{2}\b'
    data = request_ebay(url)
    html = HTMLParser(data)
    specs = html.css_first('div #viTabs_0_is')
    divs = specs.css('div .ux-labels-values__values-content')
    for div in divs:
        text = div.text()
        print('div text is: ' + text)
        if re.search(pattern, text):
            print(text)
            output_dict[url] = text
            break
    sleep(1)


def export_anums_to_json(ebay_itm_hrefs:list):
    anum_dict = {}
    for href in ebay_itm_hrefs:
        extract_anum_from_listing_url(href, anum_dict)

    with open('anums.json', 'w') as f:
        json.dump(anum_dict, f)


def extract_anum_from_ebay_workflow(username:str, max_page:int=3, keyword:str='ikea'):
    ebay_itm_hrefs = get_ebay_listings_from_username(username, max_page=max_page, keyword=keyword)
    export_anums_to_json(ebay_itm_hrefs)