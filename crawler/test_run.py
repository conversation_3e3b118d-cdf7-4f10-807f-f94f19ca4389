import os, pytest, subprocess, unittest

from pathlib import Path
from PIL import Image
import numpy as np
from time import sleep
from playwright.sync_api import sync_playwright
from unittest.mock import Mock

from src.pdf_helper import JPG_EXTENSION, PNG_EXTENSION, extract_part_numbers_from_pdf_pymupdf, parse_parts_regex, read_part_number_from_image, read_part_number_from_images
from src.utils_crawler import fetch_product_detail
from src.data_access import check_if_pdf_exists, execute_transaction
from src.product_parser import ProductParser
from model.error_handler import MyException
from model.product_model import Pdf, Product



##################################### Static constant #####################################
TEST_DIR = Path('crawler') / 'test-data'
TEST_DATA_DIR = TEST_DIR / 'data'
TEST_DATA_EXPECTED = TEST_DIR / 'expected'

LIB_DIR = TEST_DIR / 'lib'
PDF_DIR = LIB_DIR / 'pdf'
MODEL_DIR = LIB_DIR / 'model'

C_HOST_STRING = 'C_HOST'
C_PORT_STRING = 'C_PORT'
C_PASSWORD_STRING = 'C_PASSWORD'
DATABASE_STRING = 'DATABASE'
BUCKET_NAME_STRING = 'BUCKET_NAME'
CLOUD_STORAGE_URL_STRING = 'CLOUD_STORAGE_URL'
BUNNY_API_KEY_STRING = 'BUNNY_API_KEY'

C_HOST_TEST = 'localhost'
C_PORT_TEST = '3306'
C_PASSWORD_TEST = 'jim12345'
DATABASE_TEST = 'test_db'
BUCKET_NAME_TEST = 'dev-ikea-lib'
CLOUD_STORAGE_URL_TEST = 'https://dev.easyrebuild.com'
BUNNY_API_KEY_TEST = '48469e06-b0f7-4a25-98a46221251e-104e-4cb8'

TEST_ENV_VARS = {
    C_HOST_STRING: C_HOST_TEST,
    C_PORT_STRING: C_PORT_TEST,
    C_PASSWORD_STRING: C_PASSWORD_TEST,
    DATABASE_STRING: DATABASE_TEST,
    BUCKET_NAME_STRING: BUCKET_NAME_TEST,
    CLOUD_STORAGE_URL_STRING: CLOUD_STORAGE_URL_TEST,
    BUNNY_API_KEY_STRING: BUNNY_API_KEY_TEST
}

##################################### Crawler Test section #####################################    

########### Helper functions ###########
def lists_have_same_contents(list1:list, list2:list):
    return sorted(list1) == sorted(list2)

def images_are_identical(img1_path, img2_path):
    img1 = Image.open(img1_path)
    img2 = Image.open(img2_path)
    return np.array_equal(np.array(img1), np.array(img2))

def remove_dir(dir_path):
    for root, dirs, files in os.walk(dir_path, topdown=False):
        for name in files:
            os.remove(os.path.join(root, name))
        for name in dirs:
            os.rmdir(os.path.join(root, name))
    os.rmdir(dir_path)

def env_var_sanity_check():
    assert os.environ[C_HOST_STRING] == C_HOST_TEST
    assert os.environ[C_PORT_STRING] == C_PORT_TEST
    assert os.environ[C_PASSWORD_STRING] == C_PASSWORD_TEST
    assert os.environ[DATABASE_STRING] == DATABASE_TEST
    assert os.environ[BUCKET_NAME_STRING] == BUCKET_NAME_TEST
    assert os.environ[CLOUD_STORAGE_URL_STRING] == CLOUD_STORAGE_URL_TEST
    assert os.environ[BUNNY_API_KEY_STRING] == BUNNY_API_KEY_TEST

def assert_pdf(pdf, folder_name, img_count, parts):
    subdir = PDF_DIR / folder_name
    assert os.path.exists(subdir), "The pdf directory was not created"
    assert os.path.exists(subdir / f'{folder_name}.pdf'), "The pdf file was not downloaded"
    assert os.path.exists(subdir / 'view'), "The pdf view directory was not created"
    assert os.path.exists(subdir / 'view' / f'p1.{PNG_EXTENSION}'), "The page image was not created"
    assert len(os.listdir(subdir / 'view')) == img_count, "page count not match"

    assert isinstance(pdf, Pdf), "The pdf is not PDF object"
    assert pdf.file_name == f'{folder_name}.pdf', "The pdf name not correct"
    assert pdf.file_path == f'crawler/test-data/lib/pdf/{folder_name}/view', "The pdf path not correct"
    assert pdf.page_count == img_count, "The page count not match"
    assert lists_have_same_contents(pdf.parts, parts), "parts not match"

########### Test cases ###########
def test_env_for_deploy():
    # XXX if any test fails because of mismatching database, kill all instance of python.exe.
    # for making sure the environment variables are set correctly for production
    # XXX always run first because later tests will overwrite the environment variables
    from dotenv import load_dotenv
    load_dotenv()

    # crawler 
    assert os.getenv(C_HOST_STRING) == '***********'
    assert os.getenv(C_PORT_STRING) == '3308'
    assert os.getenv(DATABASE_STRING) == 'ik_schema2'
    assert os.getenv(BUCKET_NAME_STRING) == 'manuals-lib'
    # flask app
    assert os.getenv('USER') == 'root'
    assert os.getenv('HOST') == 'localhost'
    assert os.getenv('PORT') == '3306'
    # bunny
    assert os.getenv(CLOUD_STORAGE_URL_STRING) == 'https://photos.easyrebuild.com'


def test_playground(db_fixture):
    pdf_file = TEST_DATA_DIR / 'godmorgon-sink-cabinet-with-4-drawers-kasjoen-white__AA-2033515-6-2.pdf'
    pdf_file = TEST_DATA_DIR / 'utrusta-hinge-w-b-in-damper-for-kitchen__AA-2033963-1-2.pdf'
    pdf_file = TEST_DATA_DIR / 'besta-frame-white__AA-1272094-8-100.pdf'
    original_img = TEST_DATA_DIR / f'p1_utrusta_wide.{PNG_EXTENSION}'

    # expected_set  = {'126918', '126916', '141413'}
    # convert_pdf_to_img(pdf_file, 'test111', 'crawler')
    # result = read_part_number_from_image(original_img)
    # # result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    # print(result)
    # print()


def test_product_parser(db_fixture):
    # db_fixture is a fixture that sets up the test_db database
    os.environ.update(TEST_ENV_VARS)
    env_var_sanity_check()

    if os.path.exists(LIB_DIR):
        remove_dir(LIB_DIR)
    
    #### case 1: 09337677: Product Name: IKEA UPPLAND Ottoman, Totebo light beige
    data = fetch_product_detail('09337677')
    data = data['data'][0]
    parser = ProductParser(data, for_test=True)
    if parser.should_crawl():
        success = parser.execute()
        # assert successfully executed
        assert success, "The parser should have executed successfully"
        assert parser.is_ocr_finished and parser.is_pymupdf_finished, "read parts must be done"
        assert parser.pdf_skipped_count == 0, "no pdf should be skipped"
        assert parser.upload_skipped_count == 0, "no upload for pdf img should be skipped"

        product = parser.create_product()
        assert isinstance(product, Product), "The product is not Product object"
        # assert folder structure
        assert os.path.exists(MODEL_DIR), "The model directory was not created"
        assert os.path.exists(PDF_DIR), "The pdf directory was not created"
        # assert model images
        model_folder_name = 'UPPLAND-ottoman-09337677'
        assert os.path.exists(MODEL_DIR / model_folder_name), "The model directory was not created"
        number_model_img = len(os.listdir(MODEL_DIR / model_folder_name))
        assert number_model_img == 2, "total model images must be 2"
        # assert main and thumbnail image
        assert os.path.exists(MODEL_DIR / model_folder_name / f'main_.{JPG_EXTENSION}'), "The main image was not created"
        assert os.path.exists(MODEL_DIR / model_folder_name / f'thumb_.{JPG_EXTENSION}'), "The thumbnail image was not created"

        # assert metadata
        assert product.name == 'UPPLAND', "The product name was not parsed correctly"
        assert product.product_type == 'ottoman', "The product type was not parsed correctly"
        assert product.product_num == '09337677', "The product number was not parsed correctly"
        assert isinstance(product.details, str), "The details is not a string"
        assert '["You can put things like magazines and toys in the storage space under the seat.", ' in product.details, "The details were not parsed correctly"
        assert isinstance(product.measurements, str), "The measurements is not a string"
        assert '[{"type": "00001", "typeName": "Length", "textMetric": "82 cm"' in product.measurements, "The measurements were not parsed correctly"
        assert product.image_dir == f'crawler/test-data/lib/model/{model_folder_name}', "The image directory was not created"
        assert product.name_w_type == 'UPPLAND Ottoman, Totebo light beige', "The name with type was not parsed correctly"

        # assert pdfs
        sorted_pdfs = sorted(product.pdfs, key=lambda pdf: pdf.file_name)
        expected_pdf_name1 = 'uppland-cover-for-ottoman-with-storage-totebo-light-beige__AA-2202691-1-2'
        pdf1 = sorted_pdfs[0]
        expected_count1 = 4
        subdir = PDF_DIR / expected_pdf_name1
        assert os.path.exists(subdir), "The pdf directory was not created"
        assert os.path.exists(subdir / f'{expected_pdf_name1}.pdf'), "The pdf file was not downloaded"
        assert os.path.exists(subdir / 'view'), "The pdf view directory was not created"
        assert os.path.exists(subdir / 'view' / f'p1.{PNG_EXTENSION}'), "The page image was not created"
        assert len(os.listdir(subdir / 'view')) == expected_count1, "page count not match"

        assert isinstance(pdf1, Pdf), "The pdf is not PDF object"
        assert pdf1.file_name == f'{expected_pdf_name1}.pdf', "The pdf name not correct" #XXX file path is correct, in production it should be just lib/pdf/{pdf_folder_name1}/view
        assert pdf1.file_path == f'crawler/test-data/lib/pdf/{expected_pdf_name1}/view', "The pdf path not correct"
        assert pdf1.page_count == expected_count1, "The page count not match"
        assert lists_have_same_contents(pdf1.parts, []), "should empty"

        pdf_name2 = 'uppland-ottoman-frame-with-storage__AA-2202686-1-2'
        pdf2 = sorted_pdfs[1]
        expected_count2 = 4
        subdir = PDF_DIR / pdf_name2
        assert os.path.exists(subdir), "The pdf directory was not created"
        assert os.path.exists(subdir / f'{pdf_name2}.pdf'), "The pdf file was not downloaded"
        assert os.path.exists(subdir / 'view'), "The pdf view directory was not created"
        assert os.path.exists(subdir / 'view' / f'p1.{PNG_EXTENSION}'), "The page image was not created"
        assert len(os.listdir(subdir / 'view')) == expected_count2, "page count not match"

        assert isinstance(pdf2, Pdf), "The pdf is not PDF object"
        assert pdf2.file_name == f'{pdf_name2}.pdf', "The pdf name not correct" #XXX file path is correct, in production it should be just lib/pdf/{pdf_folder_name1}/view
        assert pdf2.file_path == f'crawler/test-data/lib/pdf/{pdf_name2}/view', "The pdf path not correct"
        assert pdf2.page_count == expected_count2, "The page count not match"
        assert lists_have_same_contents(pdf2.parts, ['100001', '105105', '112353']), "should be 3" # uncomment later

    
    #### case 2: same product, same pdf and and img, so skipped count = 2 and read part flags should be false
    data = fetch_product_detail('09337677')
    data = data['data'][0]
    parser = ProductParser(data, for_test=True)
    if parser.should_crawl():
        success = parser.execute()
        assert success, "The parser should have executed successfully"
        assert not parser.is_ocr_finished and not parser.is_pymupdf_finished, "no need to read parts because they share the same pdfs"
        assert parser.pdf_skipped_count == 2, "2 pdfs should be skipped"
        assert parser.upload_skipped_count == 2, "2 uploads for pdf img should be skipped"


    #### case 3: 993.249.15 UPPLAND Ottoman, Virestad red/white. Share 1 exact match pdf name (the frame) and 
    #### 1 match for aaid (the cover), so skipped count = 2 and read part flags should be false
    data = fetch_product_detail('99324915')
    data = data['data'][0]
    parser = ProductParser(data, for_test=True)
    if parser.should_crawl():
        success = parser.execute()
        assert success, "The parser should have executed successfully"
        assert not parser.is_ocr_finished and not parser.is_pymupdf_finished, "no need to read parts because they share the same pdfs"
        assert parser.pdf_skipped_count == 2, "2 pdf should be skipped"
        assert parser.upload_skipped_count == 2, "2 uploads for pdf img should be skipped"
        product = parser.create_product()
        assert isinstance(product, Product), "The product is not Product object"
        # assert pdfs
        sorted_pdfs = sorted(product.pdfs, key=lambda pdf: pdf.file_name)
        expected_pdf_aaid = 'AA-2202691-1-2'
        expected_pdf_name1 = f'uppland-cover-for-ottoman-with-storage-virestad-red-white__{expected_pdf_aaid}.pdf'
        # share the same pdf path as the first case because they share the same pdf aaid
        expected_pdf_path1= f'uppland-cover-for-ottoman-with-storage-totebo-light-beige__{expected_pdf_aaid}' 
        pdf1 = sorted_pdfs[0]
        expected_count1 = 4

        assert isinstance(pdf1, Pdf), "The pdf is not PDF object"
        assert pdf1.file_name == f'{expected_pdf_name1}', "The pdf name not correct" 
        # XXX file path is correct, in production it should be just lib/pdf/{pdf_folder_name1}/view
        assert pdf1.file_path == f'crawler/test-data/lib/pdf/{expected_pdf_path1}/view', "The pdf path not correct"
        assert pdf1.page_count == expected_count1, "The page count not match"
        assert lists_have_same_contents(pdf1.parts, []), "should empty"

        pdf_name2 = 'uppland-ottoman-frame-with-storage__AA-2202686-1-2'
        pdf2 = sorted_pdfs[1]
        expected_count2 = 4

        assert isinstance(pdf2, Pdf), "The pdf is not PDF object"
        assert pdf2.file_name == f'{pdf_name2}.pdf', "The pdf name not correct" 
        # XXX file path is correct, in production it should be just lib/pdf/{pdf_folder_name1}/view
        assert pdf2.file_path == f'crawler/test-data/lib/pdf/{pdf_name2}/view', "The pdf path not correct"
        assert pdf2.page_count == expected_count2, "The page count not match"
        assert lists_have_same_contents(pdf2.parts, ['100001', '105105', '112353']), "should be 3" # uncomment later

    # clean up
    remove_dir(LIB_DIR)


def test_OCR_read_part():
    ## test rotating image to get the most accurate result
    ## and the cloned image should be removed after parsing
    ## OCR Engine 2 seems to hallucinate some part numbers if image is not rotated to 270 degree.

    # case 00: should have 5 parts, OCRengine 1 only finds 4 parts, used OCRengine 2
    pdf_img_dir = TEST_DATA_DIR / 'gronlid_corner__AA-2053802-1-2'
    result:set[str] = read_part_number_from_images(pdf_img_dir, isPro=False)
    expected_parts = {'108490', '100032', '110439', '100854', '104864'}
    assert result == expected_parts, "The part numbers were not extracted correctly"

    # case 01: should have 7 parts, OCRengine 1 finds all
    pdf_img_dir = TEST_DATA_DIR / 'gronlid_armrest__AA-2053714-1-2'
    result:set[str] = read_part_number_from_images(pdf_img_dir, isPro=False)
    expected_parts = {'100854', '110737', '100712', '130646', '104864', '108490', '120202'}
    assert result == expected_parts, "The part numbers were not extracted correctly"

    # case 02: should have 5 parts, OCRengine 1 only finds 3 parts, used OCRengine 2
    pdf_img_dir = TEST_DATA_DIR / 'karlstad__AA-254508-2-2'
    result:set[str] = read_part_number_from_images(pdf_img_dir, isPro=False)
    expected_parts = {'100178', '101350', '104601', '117142', '117145'}
    assert result == expected_parts, "The part numbers were not extracted correctly"

    # case 03: should have 3 parts, OCRengine 1 finds 4 parts, used OCRengine 2 find 3 parts which is correct
    pdf_img_dir = TEST_DATA_DIR / 'komplement__AA-2181214-3-2'
    result:set[str] = read_part_number_from_images(pdf_img_dir, isPro=False)
    expected_parts = {'101558', '115344', '10077788'}
    assert result == expected_parts, "The part numbers were not extracted correctly"

    # case 1: should have 3 parts when rotated
    p2_komplement_FN = f'p2_komplement.{PNG_EXTENSION}'
    original_img = TEST_DATA_DIR / p2_komplement_FN
    result = read_part_number_from_image(original_img)

    expected_parts = {'130527', '130627', '100325'}
    assert result == expected_parts, "The part numbers were not extracted correctly"
    expected_cloned_img = original_img.with_suffix(f'.copy.{PNG_EXTENSION}')
    assert not os.path.exists(expected_cloned_img), "The cloned file was not removed"
    expected_original = TEST_DATA_EXPECTED / p2_komplement_FN
    assert images_are_identical(original_img, expected_original), "The orignal image was rotated"

    # case 2: uppland should havce 6 parts
    p3_uppland_FN = f'p3_uppland.{PNG_EXTENSION}'
    original_img = TEST_DATA_DIR / p3_uppland_FN
    result = read_part_number_from_image(original_img)
    
    expected_parts = {'114509', '100837', '120202', '110439', '100712', '10046870'}
    assert result == expected_parts, "The part numbers were not extracted correctly"
    expected_cloned_img = original_img.with_suffix(f'.copy.{PNG_EXTENSION}')
    assert not os.path.exists(expected_cloned_img), "The cloned file was not removed"
    expected_original = TEST_DATA_EXPECTED / p3_uppland_FN
    assert images_are_identical(original_img, expected_original), "The orignal image was rotated"

    # case 3: tufjord should have 14 parts
    p3_tufjord_FN = f'p3_tufjord.{PNG_EXTENSION}'
    original_img = TEST_DATA_DIR / p3_tufjord_FN
    result = read_part_number_from_image(original_img)

    expected_parts = {'111631', '139301', '191315', '10050800', '130445', '100854', '104558', 
                      '117001', '112090', '192494', '101084', '105163', '110789', '101359'}
    assert result == expected_parts, "The part numbers were not extracted correctly"
    expected_cloned_img = original_img.with_suffix(f'.copy.{PNG_EXTENSION}')
    assert not os.path.exists(expected_cloned_img), "The cloned file was not removed"
    expected_original = TEST_DATA_EXPECTED / p3_tufjord_FN
    assert images_are_identical(original_img, expected_original), "The orignal image was rotated"

    # case 4: besta support leg should have 3 parts, OCR and pymupdf should both give 3 parts
    pdf_file = TEST_DATA_DIR / 'besta-support-leg-gray__AA-1252740-2-2.pdf'
    p2_besta_support_leg_FN = f'p2_besta_support_leg.{PNG_EXTENSION}'
    original_img = TEST_DATA_DIR / p2_besta_support_leg_FN

    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_set  = {'141326', '148816', '109504'}
    assert result == expected_set, "The part numbers were not extracted correctly"
    result = read_part_number_from_image(original_img)
    assert lists_have_same_contents(result, list(expected_set)), "The part numbers were not extracted correctly"
    expected_cloned_img = original_img.with_suffix(f'.copy.{PNG_EXTENSION}')
    assert not os.path.exists(expected_cloned_img), "The cloned file was not removed"
    expected_original = TEST_DATA_EXPECTED / p2_besta_support_leg_FN
    assert images_are_identical(original_img, expected_original), "The orignal image was rotated"

    # case 5: besta frame white should have 21 parts, OCR got 21 and pymupdf got 3 parts
    pdf_file = TEST_DATA_DIR / 'besta-frame-white__AA-1272094-8-100.pdf'
    p7_besta_frame_white_FN = f'p7_besta_frame_white.{PNG_EXTENSION}'
    original_img = TEST_DATA_DIR / p7_besta_frame_white_FN

    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_set  = {'126918', '126916', '141413'}
    assert result == expected_set, "muPDF should get 3 parts"
    result = read_part_number_from_image(original_img)
    expected = {'141413', '141414', '114947', '115988', '100006', '114929', '116637', '130618', 
                '114667', '122971', '130725', '118137', '119253', '141028', 
                '126916', '133194', '133195', '133196', '133303', '133304', '126918'}
    assert result == expected, "OCR should get 21 parts"
    expected_cloned_img = original_img.with_suffix(f'.copy.{PNG_EXTENSION}')
    assert not os.path.exists(expected_cloned_img), "The cloned file was not removed"
    expected_original = TEST_DATA_EXPECTED / p7_besta_frame_white_FN
    assert images_are_identical(original_img, expected_original), "The orignal image was rotated"


def test_PyMuPDF_extract_from_pdf_file():
    # case 1: should not include 938836 as part number because it is a pdf id number
    pdf_file = TEST_DATA_DIR / 'komplement-clothes-rail-white__AA-938836-2-2.pdf'
    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_parts = set()
    assert result == expected_parts, "should be empty because 938836 is not a part number"

    # case 4: not matching 568584 in AA-568584-5-2 (usually at bottom left of a page)
    pdf_file = TEST_DATA_DIR / 'tross-ceiling-track-3-spotlights-white__AA-568584-5-2.pdf'
    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_parts = set()
    assert result == expected_parts, "should be empty because 568584 is not a part number"

    # case 2: uppland has 6 part numbers but pymupdf gives 0
    pdf_file = TEST_DATA_DIR / 'uppland-armchair-frame__AA-2202684-1-2.pdf'
    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_parts = set()
    assert result == expected_parts, "Expected to not have any part numbers but OCR gives 6 parts"

    # case 3: tufjord 114291, 114290 are not first pages so OCR did not pick up
    # part list from OCR only gives 17 parts
    pdf_file = TEST_DATA_DIR / 'tufjord-upholstered-bed-frame-djuparp-dark-green__AA-2215382-3.pdf'
    result = extract_part_numbers_from_pdf_pymupdf(pdf_file)
    expected_parts = {'101359', '10050800', '192494', '150022', '117001', '100854', 
                      '112090', '139301', '101084', '110789', '10054877', 
                      '114290', '114291', '111631', '104558', '130445', '191315', '10051931', '105163'}
    assert result == expected_parts, "The part numbers were not extracted correctly"


def test_parse_part_regex():
    good = '191315\n8x\n8x\n105163\n8x\n011359\n4x\n10050800\n50x\n00001111\n'
    bad = "AA-123456-1 AA-234567 AA-12345678 A-345678-"
    mix = '150022\n10054877\n4x\n4x\n8x\\n4\nAA-2215382-3\n'
    mix2 = '5\n1x\n2x\n112090\n8\nAA-2215382-3\n'

    text = good + bad + mix + mix2
    expected = ['191315', '105163', '10050800', '150022', '10054877', '112090']
    actual = parse_parts_regex(text)
    assert actual == expected

##################################### Behavorial Test section #####################################
def test_check_if_pdf_exists():
    with unittest.mock.patch('src.data_access.create_connection') as mock_conn:
        mock_cursor = Mock()
        mock_conn.return_value.__enter__.return_value.cursor.return_value = mock_cursor

        pdf_aaid = 'AA-123.pdf'
        file_name = f'file1__{pdf_aaid}'
        file_path = f'path/to/{file_name}'.replace('.pdf', '')

        # Set up the mock cursor's fetchone method for success case
        mock_cursor.fetchone.return_value = ['test_id', file_name, file_path]
        # Test the function with a pdf_aaid that is in both the file name and file path
        result = check_if_pdf_exists(pdf_aaid)
        assert result == 'test_id'

        # Test the function with a pdf_aaid that is not in the database
        mock_cursor.fetchone.return_value = None
        result = check_if_pdf_exists('non_existent_aaid.pdf')
        assert result is None

        # Test the function with a pdf_aaid that does not contain .pdf
        with pytest.raises(MyException):
            check_if_pdf_exists('file_without_pdf_extension.txt')

        # Test the function with a pdf_aaid that is not in the file name
        mock_cursor.fetchone.return_value = ['test_id', 'other_file_name', file_path]
        with pytest.raises(MyException):
            check_if_pdf_exists(pdf_aaid)

        # Test the function with a pdf_aaid that is not in the file path
        mock_cursor.fetchone.return_value = ['test_id', file_name, 'other_file_path']
        with pytest.raises(MyException):
            check_if_pdf_exists(pdf_aaid)

##################################### Webapp Test section #####################################

########### Helper functions ###########
def assert_modal(page, search_term, expected_modal_message):
    search_input = page.query_selector('input')
    search_button = page.query_selector('#searchButton')
    search_input.fill(search_term)
    search_button.click()
    sleep(.5)

    # modal should show up
    error = page.query_selector('div#errorModal')
    assert error, "Error modal not found"
    assert 'modal fade show' in error.get_attribute('class'), "Error modal not shown"
    assert 'display: block' in error.get_attribute('style'), "Error modal not shown"
    message = error.query_selector('div#errorModalBody')
    assert message, "Error modal message not found"
    assert message.inner_text() == expected_modal_message, "Error modal message not match"

    # query for 2 buttons and assert click them to close modal
    buttons = error.query_selector_all('button')
    assert len(buttons) == 2, "2 buttons not found"
    for button in buttons:
        button.click()
        sleep(.5)
        assert 'modal fade' in error.get_attribute('class'), "Error modal not closed"
        assert 'display: none' in error.get_attribute('style'), "Error modal not closed"
        search_button.click()
        sleep(.5)

    # press escape to close modal
    page.keyboard.press('Escape')
    sleep(.5)
    assert 'modal fade' in error.get_attribute('class'), "Error modal not closed"
    assert 'display: none' in error.get_attribute('style'), "Error modal not closed"
    
def set_up_env_webapp():
    os.environ.update(TEST_ENV_VARS)
    env = os.environ.copy() # copy the test environment variables for webapp
    env_var_sanity_check()
    return env

def perform_search_and_check_results(page, search_term, expected_search_summary, expected_result, should_empty=False):
    search_input = page.query_selector('input')
    search_button = page.query_selector('#searchButton')
    search_input.fill(search_term)
    search_button.click()
    sleep(.5)
    resultContainer = page.query_selector('div#result')
    h3 = resultContainer.query_selector('h3')
    assert h3, "Search result summary title not found"
    assert h3.inner_text() == expected_search_summary, "Result summary not match"
    if should_empty:
        return
    
    assert len(resultContainer.query_selector_all('li.list-group-item')) == 1, "only 1 result should be found"
    assert resultContainer.query_selector('img'), "no thumbnail found"
    a_tag = resultContainer.query_selector('a')
    assert a_tag, "result is not an atag"
    strong = a_tag.query_selector('strong')
    assert strong, "strong tag not found"
    assert strong.inner_text() == expected_result, "result text not match, should NOT include a span to highlight search text"
    
    # only assert highlight span for name and type search
    checked_radio = page.query_selector('input[type="radio"]:checked')
    assert checked_radio, "radio input not found"
    value = checked_radio.get_attribute('value')
    if value == 'name' or value == 'type':
        span = strong.query_selector('span')
        assert span, "span tag not found"
        span.get_attribute('class') == 'bg-warning', "span class not match"
        span.inner_text() == search_term, "span text should match search term"

def click_search_result(page, expected_count, mock_product, expected_summary):
    resultContainer = page.query_selector('div#result')
    assert len(resultContainer.query_selector_all('li.list-group-item')) == expected_count, "result count not match"
    assert resultContainer.query_selector('img'), "no thumbnail found"
    a_tags = resultContainer.query_selector_all('a')
    assert a_tags, "results are not a tags"
    assert resultContainer.query_selector('h3').inner_text() == expected_summary, "Result summary not match"
    expected_fullname = f'IKEA {mock_product.name_w_type}'
    expect_name_type = f'{mock_product.name} {mock_product.product_type}'
    assert a_tags[0].inner_text() == expected_fullname, "text result not match"
    a_tags[0].click()
    sleep(.5)

    generalContainer = page.query_selector('div#generalInfo')
    title = generalContainer.query_selector('h1')
    expect_tile = f'IKEA Instructions for {expect_name_type}'
    assert title.inner_text() == expect_tile, "title in detail page not match"

def set_up_product_for_webapp(db_fixture, mock_LACK, mock_UPPLAND):
    try:
        execute_transaction(db_fixture, mock_LACK)
    except Exception as e:
        pytest.fail(f"execute_transaction raised Exception unexpectedly: {str(e)}")

    try:
        execute_transaction(db_fixture, mock_UPPLAND)
    except Exception as e:
        pytest.fail(f"execute_transaction raised Exception unexpectedly: {str(e)}")

########### Test cases ###########
def test_search_invalid():
    env = set_up_env_webapp()
    webapp = subprocess.Popen(['.\.venv\Scripts\python.exe', 'webapp/app.py'], env=env)

    sleep(2)
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto('http://localhost:5000')
        assert 'Search IKEA Manuals and Parts' in page.content()

        # invalid search term, modal show up
        assert_modal(page, '', 'Search term cannot be empty')
        assert_modal(page, '       ', 'Search term cannot be empty')
        assert_modal(page, '1', 'Too short! Search term must be at least 2 characters long') 
        assert_modal(page, '   1    ', 'Too short! Search term must be at least 2 characters long')

        browser.close()
    webapp.terminate()


def test_search_by(db_fixture, mock_LACK, mock_UPPLAND):
    set_up_product_for_webapp(db_fixture, mock_LACK, mock_UPPLAND)
    env = set_up_env_webapp()
    webapp = subprocess.Popen(['.\.venv\Scripts\python.exe', 'webapp/app.py'], env=env)
    
    sleep(2)
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto('http://localhost:5000')
        assert 'Search IKEA Manuals and Parts' in page.content()

        LACK_ANUM_DOT = '604.305.92'
        expected_fullname = f'IKEA {mock_LACK.name_w_type}'

        ### Search by product type for LACK
        expected_summary = f'Found 1 result for type "{mock_LACK.product_type}"'
        perform_search_and_check_results(page, mock_LACK.product_type, expected_summary, expected_fullname)
        click_search_result(page, 1, mock_LACK, expected_summary)
        page.go_back()
        fake_term = 'fakeType'
        expected_summary = f'No results found for type "{fake_term}". Try Search Help for more info.'
        perform_search_and_check_results(page, fake_term, expected_summary, '', should_empty=True)

        ### Search by name for LACK by default
        page.query_selector('#nameRadio').click()
        expected_summary = f'Found 1 result for name "{mock_LACK.name}"'
        perform_search_and_check_results(page, mock_LACK.name, expected_summary, expected_fullname)
        click_search_result(page, 1, mock_LACK, expected_summary)
        page.go_back()
        fake_term = 'fakeName'
        expected_summary = f'No results found for name "{fake_term}". Try Search Help for more info.'
        perform_search_and_check_results(page, fake_term, expected_summary, '', should_empty=True)

        ### Search by article number for LACK with '60430592' and '604.305.92'
        page.query_selector('#anumRadio').click()
        expected_summary = f'Found 1 result for article number "{mock_LACK.product_num}"'
        perform_search_and_check_results(page, mock_LACK.product_num, expected_summary, expected_fullname)
        click_search_result(page, 1, mock_LACK, expected_summary)
        page.go_back()
        expected_summary = f'Found 1 result for article number "{LACK_ANUM_DOT}"'
        perform_search_and_check_results(page, LACK_ANUM_DOT, expected_summary, expected_fullname)
        click_search_result(page, 1, mock_LACK, expected_summary)
        page.go_back()
        fake_term = '12345678'
        expected_summary = f'No results found for article number "{fake_term}". Try Search Help for more info.'
        perform_search_and_check_results(page, fake_term, expected_summary, '', should_empty=True)

        ### Search by part number for 100001; should give 2 results for LACK and UPPLAND
        page.query_selector('#partRadio').click()
        fake_term = '000111'
        expected_summary = f'No results found for part "{fake_term}". Try Search Help for more info.'
        perform_search_and_check_results(page, fake_term, expected_summary, '', should_empty=True)
        
        search_input = page.query_selector('input')
        search_button = page.query_selector('#searchButton')
        search_term = '100001'
        search_input.fill(search_term)
        search_button.click()
        sleep(.5)
        resultContainer = page.query_selector('div#result')
        assert len(resultContainer.query_selector_all('li.list-group-item')) == 2, "2 results should be found"
        assert resultContainer.query_selector('img'), "no thumbnail found"
        a_tags = resultContainer.query_selector_all('a')
        assert a_tags, "result is not an atag"
        expected_summary = f'Found 2 results for part "{search_term}"'
        assert resultContainer.query_selector('h3').inner_text() == expected_summary, "Result summary not match"
        assert a_tags[0].inner_text() == expected_fullname, "LACK text result not match"
        expected_fullname = f'IKEA {mock_UPPLAND.name_w_type}'
        assert a_tags[1].inner_text() == expected_fullname, "UPPLAND text result not match"

        ### click the second tag to go to details page of UPPLAND
        expect_name_type = f'{mock_UPPLAND.name} {mock_UPPLAND.product_type}'
        expected_fullname = f'IKEA {mock_UPPLAND.name_w_type}'
        a_tags[1].click()
        sleep(.5)

        # assert general info
        generalContainer = page.query_selector('div#generalInfo')
        title = generalContainer.query_selector('h1')
        expect_tile = f'IKEA Instructions for {expect_name_type}'
        assert title.inner_text() == expect_tile, "title not match"

        browser.close()
    webapp.terminate()


def test_detail_page(db_fixture, mock_LACK, mock_UPPLAND):
    set_up_product_for_webapp(db_fixture, mock_LACK, mock_UPPLAND)
    env = set_up_env_webapp()
    webapp = subprocess.Popen(['.\.venv\Scripts\python.exe', 'webapp/app.py'], env=env)
    
    sleep(2)
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto('http://localhost:5000/d/2/ikea-instructions-uppland-ottoman-59322409?q=100001')

        ### click the second tag to go to details page of UPPLAND
        UPPLAND_ANUM_DOT = '593.224.09'
        expect_name_type = f'{mock_UPPLAND.name} {mock_UPPLAND.product_type}'
        expected_manual_count = len(mock_UPPLAND.pdfs)
        expected_fullname = f'IKEA {mock_UPPLAND.name_w_type}'

        # assert general info
        generalContainer = page.query_selector('div#generalInfo')
        title = generalContainer.query_selector('h1')
        expect_tile = f'IKEA Instructions for {expect_name_type}'
        assert title.inner_text() == expect_tile, "title not match"
        expected_general_info = f'{expect_tile}\n\nProduct Name: {expected_fullname}\nArticle Number: {UPPLAND_ANUM_DOT}\nNumber of user manuals: {expected_manual_count}'
        assert generalContainer.inner_text() == expected_general_info, "general info not match"

        # assert TOC
        toc = page.query_selector('div#tableOfContent')
        assert toc, "toc not found"
        expected_toc = 'Table of Content\n1) Product Manual\n2) Parts List\n3) Product Detail\n4) Question and Answer\n5) Related Products\n6) FAQ'
        assert toc.inner_text() == expected_toc, "toc not match"

        # assert product manual
        manualContainer = page.query_selector('div#manualContainer')
        h5 = manualContainer.query_selector('h5')
        expected_h5 = f'There are {expected_manual_count} assembly instruction manuals for IKEA {expect_name_type}.\nScroll down to view all'
        assert h5.inner_text() == expected_h5, "manual count title not match"

        # should have 2 view-container class
        viewerContainers = page.query_selector_all('div[id^="viewerContainer"]')
        assert len(viewerContainers) == expected_manual_count, "manual count not match"
        for i in range(expected_manual_count):
            viewerContainer = viewerContainers[i]
            assert viewerContainer, "viewerContainer not found"
            # page count
            page_count = viewerContainer.get_attribute('page-count')
            expected_page_count = str(mock_UPPLAND.pdfs[i].page_count)
            assert page_count == expected_page_count, "page count not match"
            # img src
            imgViewer = viewerContainer.query_selector('img')
            assert imgViewer, "imgViewer not found"
            src = imgViewer.get_attribute('src')
            first_page = f'{CLOUD_STORAGE_URL_TEST}/{mock_UPPLAND.pdfs[i].file_path}/p1.{PNG_EXTENSION}'
            assert src == first_page, "img src not match"
            # assert left/right prompt
            left_prompt = viewerContainer.query_selector('div#leftPrompt')
            right_prompt = viewerContainer.query_selector('div#rightPrompt')
            assert left_prompt, "left prompt not found"
            assert right_prompt, "right prompt not found"
            expected_animation = 'animation: 2s ease 0s infinite normal none running pulse;'
            expected_left_img = 'background-image: url("https://photos.easyrebuild.com/static/lef.png")'
            expected_right_img = 'background-image: url("https://photos.easyrebuild.com/static/righ.png")'
            left_style = left_prompt.get_attribute('style')
            assert expected_animation in left_style, "Animation not found"
            assert 'display: none' not in left_style, "left prompt should be visible"
            assert expected_left_img in left_style, "left img not found"
            right_style = right_prompt.get_attribute('style')
            assert expected_animation in right_style, "Animation not found"
            assert 'display: none' not in right_style, "right prompt should be visible"
            assert expected_right_img in right_style, "right img not found"

            # click buttons
            buttons = viewerContainer.query_selector_all('button')
            assert len(buttons) == 2, "Left / Right buttons not found"
            left = buttons[0]
            right = buttons[1]
            # click left button
            left.click()
            left_style = left_prompt.get_attribute('style')
            assert 'display: none' in left_style, "left prompt should NOT be visible"
            right_style = right_prompt.get_attribute('style')
            assert 'display: none' in right_style, "right prompt should NOT be visible"
            sleep(.5)
            last_page = f'{CLOUD_STORAGE_URL_TEST}/{mock_UPPLAND.pdfs[i].file_path}/p{page_count}.{PNG_EXTENSION}'
            assert imgViewer.get_attribute('src') == last_page, "not last page"
            # click right button
            right.click()
            left_style = left_prompt.get_attribute('style')
            assert 'display: none' in left_style, "left prompt should NOT be visible"
            right_style = right_prompt.get_attribute('style')
            assert 'display: none' in right_style, "right prompt should NOT be visible"
            sleep(.5)
            assert imgViewer.get_attribute('src') == first_page, "not first page" 
            # fill input
            input = viewerContainer.query_selector('input')
            input.fill('3')
            input.press('Enter')
            sleep(.5)
            assert imgViewer.get_attribute('src') == f'{CLOUD_STORAGE_URL_TEST}/{mock_UPPLAND.pdfs[i].file_path}/p3.{PNG_EXTENSION}', "not page 3"

        # should have 2 part lists
        partLists = page.query_selector_all('div[id^="partList"]')
        assert len(partLists) == 2, "part list count not match"
        
        def assert_part_list(partList, expected_parts:list, expected_h5):
            h5 = partList.query_selector('h5')
            assert h5.inner_text() == expected_h5, "part list title not match"
            parts = partList.query_selector_all('span')
            assert len(parts) == len(expected_parts), "parts count not match"
            part_list = [part.inner_text() for part in parts]
            assert lists_have_same_contents(part_list, expected_parts), "parts not match"
        
        first_pdf_ix = 0
        second_pdf_ix = 1
        assert_part_list(partLists[first_pdf_ix], mock_UPPLAND.pdfs[first_pdf_ix].parts, 'No hardware or fittings found for Uppland cover for ottoman with storage remmarn light gray.')
        assert_part_list(partLists[second_pdf_ix], mock_UPPLAND.pdfs[second_pdf_ix].parts, 'IKEA parts list for Uppland ottoman frame with storage:')

        # product detail
        productDetail = page.query_selector('div#productDetailContainer')
        model_img = productDetail.query_selector('img#mainImg')
        assert model_img, "image not found"
        src = model_img.get_attribute('src')
        model_img = f'{CLOUD_STORAGE_URL_TEST}/{mock_UPPLAND.image_dir}/main_.jpg'
        assert src == model_img, "model src not match"

        h2 = productDetail.query_selector('h2')
        expected_h2 = f'IKEA {mock_UPPLAND.name} Details'
        assert h2.inner_text() == expected_h2, "title not match"
        # assert details
        detail_section = productDetail.query_selector_all('section')[0]
        expected_details = 'IKEA UPPLAND Ottoman, Remmarn light gray\nArticle number: 593.224.09\n\nYou can put things like magazines and toys in the storage space under the seat.\n\nThe cover is easy to keep clean since it is removable and can be machine washed.\n\nA range of coordinated covers makes it easy for you to give your furniture a new look.\n\nWorks as an extra seat or to put things on.\n\n10-year limited warranty. Read about the terms in the limited warranty brochure.'
        assert detail_section.inner_text() == expected_details, "details not match"
        # assert measurements
        measurements_section = productDetail.query_selector_all('section')[1]
        expected_measurements = 'Length: 32 1/4 (82 cm)\nWidth: 24 3/8 (62 cm)\nHeight: 18 1/2 (47 cm)\n'
        assert measurements_section.inner_text() == expected_measurements, "measurements not match"

        # ask a question
        qna = page.query_selector('div#askQuestionContainer')
        assert qna, "ask a question not found"

        # question and answer
        qna = page.query_selector('div#questionAnswerContainer')
        assert qna, "question and answer not found"

        # related products
        relatedProducts = page.query_selector('div#relatedProductsContainer')
        assert relatedProducts, "related products not found"

        # faq
        faq = page.query_selector('div#faq')
        assert faq, "faq not found"

        browser.close()
    webapp.terminate()