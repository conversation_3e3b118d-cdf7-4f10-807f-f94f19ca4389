from enum import Enum
import traceback

class ErrorLevel(Enum):
    INFO = 1
    WARNING = 2
    MAJOR = 3

class MyException(Exception):
    def __init__(self, message, level, parent_exception=None):
        self.message = message
        self.level = level
        if parent_exception is not None:
            self.traceback = traceback.format_exception(type(parent_exception), parent_exception, parent_exception.__traceback__)
        else:
            self.traceback = traceback.format_exc()
        super().__init__(self.message)

    def __str__(self):
        return f'{self.message} - Level: {self.level}'
    

class Error_Handler:
    def __init__(self):
        self.error_list = []
        self.error_level = ErrorLevel.INFO

    def handle(self, error_message, level=ErrorLevel.INFO):
        if level == ErrorLevel.MAJOR:
            raise MyException(error_message, level)




    def append_error(self, error_message, level=ErrorLevel.INFO):
        self.error_list.append(error_message)
        self.set_error_level(level)
    
    def set_error_level(self, new_error_level):
        if new_error_level.value > self.error_level.value:
                self.error_level = new_error_level

    def has_major_error(self):
        return self.error_level == ErrorLevel.MAJOR