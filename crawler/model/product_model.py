from pydantic import BaseModel
from typing import List, Any, Optional

class Pdf(BaseModel):
    file_path: str
    file_name: str
    page_count: int
    parts: List[str]

    def __str__(self) -> str:
        return f"""name: {self.file_name} -- page_count: {self.page_count} -- total parts: {len(self.parts)}
        path: {self.file_path}"""
        
        

class Product(BaseModel):
    name: str
    name_w_type: str
    image_dir: str
    product_num: str
    details: str # should be json.dumps to string for database insertion
    measurements: Optional[Any] # should be json.dumps to string for database insertion
    product_type: str
    pdfs: List[Pdf]

    def __str__(self) -> str:
        output = f"""Product metadata:
        name: {self.name} -- name_w_type: {self.name_w_type} 
        product_type: {self.product_type} -- product_num: {self.product_num}
        image_dir: {self.image_dir}
        details: has {len(self.details)} characters
        measurements: has {len(self.measurements)} characters
        """

        for i, pdf in enumerate(self.pdfs, start=1):
            output += f"\nPdf {i}: {str(pdf)}"  # print all fields of each Pdf

        return output