import mysql.connector
import pytest
from unittest.mock import Mock
from src.data_access import execute_transaction, Product, Pdf

@pytest.fixture
def db_fixture():
    """ 
    If test fails, check if the tables are created matching with table_creations script
    This fixture creates a new database for testing and yields a connection to it.
    """
    conn = mysql.connector.connect(user='root', password='jim12345', host='localhost')
    cursor = conn.cursor()

     # Assert that innodb_ft_min_token_size is set to 1 for FULLTEXT index search on furniture type
    cursor.execute("SHOW VARIABLES LIKE 'innodb_ft_min_token_size';")
    result = cursor.fetchone()
    assert result[1] == '1', "innodb_ft_min_token_size is not set to 1"

    # Create a new database for testing
    cursor.execute("CREATE DATABASE IF NOT EXISTS test_db;")
    cursor.execute("USE test_db;")

    # Create the necessary tables
    cursor.execute("""CREATE TABLE Furniture (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    name VARCHA<PERSON>(255),
                    product_num VARCHAR(255),
                    product_type VARCHAR(255),
                    name_w_type TEXT,
                    details TEXT,
                    measurements TEXT,
                    image_dir VARCHAR(255),
                    FULLTEXT(name_w_type)
                );""")
    
    # Assert that name_w_type is a FULLTEXT index
    cursor.execute("SHOW INDEX FROM Furniture WHERE Key_name = 'name_w_type';")
    result = cursor.fetchone()
    assert result is not None, "name_w_type is not a FULLTEXT index"

    cursor.execute("""CREATE TABLE PdfFile (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    file_name VARCHAR(255) UNIQUE,
                    file_path VARCHAR(255),
                    page_count INT);""")
                
    cursor.execute("""CREATE TABLE FurniturePdf (
                    furniture_id INT,
                    pdf_file_id INT,
                    PRIMARY KEY (furniture_id, pdf_file_id),
                    FOREIGN KEY (furniture_id) REFERENCES Furniture(id),
                    FOREIGN KEY (pdf_file_id) REFERENCES PdfFile(id));""")

    cursor.execute("""CREATE TABLE Part (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    part_number VARCHAR(255) UNIQUE);""")

    cursor.execute("""CREATE TABLE PdfPart (
                    pdf_file_id INT,
                    part_id INT,
                    PRIMARY KEY (pdf_file_id, part_id),
                    FOREIGN KEY (pdf_file_id) REFERENCES PdfFile(id),
                    FOREIGN KEY (part_id) REFERENCES Part(id));""")

    conn.commit()

    yield conn

    # Drop the test database and close the connection after each test
    cursor.execute("DROP DATABASE test_db;")
    conn.close()

@pytest.fixture
def mock_LACK():
    # Create a mock Product and Pdf
    product = Mock(spec=Product)
    pdf = Mock(spec=Pdf)

    # Set up the mock Product and Pdf
    product.name = 'LACK'
    product.name_w_type = 'LACK Wall shelf unit, white stained oak effect, 11 3/4x74 3/4 "'
    product.details = '["Shallow shelves help you to use the walls in your home efficiently. They hold a lot of things without taking up much space in the room.", "Choose if you want to mount the shelf horizontally or vertically on the wall.", "A tight space or an unused wall? This shelf almost always fits. You can also choose to hang it vertically or horizontally depending on space and storage needs."]'
    product.product_num = '60430592'
    product.image_dir = 'lib/model/LACK-wall-shelf-unit-60430592'
    product.measurements = '[{"type": "00047", "typeName": "Width", "textMetric": "30 cm", "textImperial": "11 3/4 "}, {"type": "00044", "typeName": "Depth", "textMetric": "28 cm", "textImperial": "11 "}, {"type": "00041", "typeName": "Height", "textMetric": "190 cm", "textImperial": "74 3/4 "}, {"type": "00012", "typeName": "Max. load", "textMetric": "25 kg", "textImperial": "55 lb"}, {"type": "00011", "typeName": "Max load/shelf", "textMetric": "3 kg", "textImperial": "7 lb"}]'
    product.product_type = 'wall shelf unit'
    product.pdfs = [pdf]

    pdf.file_name = 'lack-wall-shelf-unit-white-stained-oak-effect__AA-419551-9-100.pdf'
    pdf.file_path = 'lib/pdf/lack-wall-shelf-unit-white-stained-oak-effect__AA-419551-9-100/view'
    pdf.page_count = 12
    pdf.parts = ['100001', '104323', '104325', '109053', '120942', '120943', '120944']

    yield product

@pytest.fixture
def mock_UPPLAND():
    # Create a mock Product and Pdf
    product = Mock(spec=Product)
    pdf1 = Mock(spec=Pdf)
    pdf2 = Mock(spec=Pdf)

    product.name = 'UPPLAND'
    product.name_w_type = 'UPPLAND Ottoman, Remmarn light gray'
    product.details = '["You can put things like magazines and toys in the storage space under the seat.", "The cover is easy to keep clean since it is removable and can be machine washed.", "A range of coordinated covers makes it easy for you to give your furniture a new look.", "Works as an extra seat or to put things on.", "10-year limited warranty. Read about the terms in the limited warranty brochure."]'
    product.product_num = '59322409'
    product.image_dir = 'lib/model/UPPLAND-ottoman-59322409'
    product.measurements = '[{"type": "00001", "typeName": "Length", "textMetric": "82 cm", "textImperial": "32 1/4 "}, {"type": "00047", "typeName": "Width", "textMetric": "62 cm", "textImperial": "24 3/8 "}, {"type": "00041", "typeName": "Height", "textMetric": "47 cm", "textImperial": "18 1/2 "}]'
    product.product_type = 'ottoman'
    product.pdfs = [pdf1, pdf2]

    pdf1.file_name = 'uppland-cover-for-ottoman-with-storage-remmarn-light-gray__AA-2202691-1-2.pdf'
    pdf1.file_path = 'lib/pdf/uppland-cover-for-ottoman-with-storage-remmarn-light-gray__AA-2202691-1-2/view'
    pdf1.page_count = 4
    pdf1.parts = []

    pdf2.file_name = 'uppland-ottoman-frame-with-storage__AA-2202686-1-2.pdf'
    pdf2.file_path = 'lib/pdf/uppland-ottoman-frame-with-storage__AA-2202686-1-2/view'
    pdf2.page_count = 4
    pdf2.parts = ['100001', '105105', '112353']

    

    yield product