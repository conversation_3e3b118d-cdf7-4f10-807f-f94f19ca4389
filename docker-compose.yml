version: "3.3"
services:
  webapp: # name must be specified in conf file in "proxy" directory
    build: ./webapp # the directory contains the Dockerfile
    restart: always
    ports:
      - 5000:5000
    environment:
      # 1 for development, 0 for production
      # if production, use gunicorn to serve the app (change dockerfile in web1 folder)
      # if development, use flask run debug=True (change dockerfile in web1 folder)
      DEBUG: "0" 
      # database fields, the app will get the value by os.getenv('USER')
      USER: ${USER}
      PASSWORD: ${MYSQL_ROOT_PASSWORD}
      HOST: db # Use this mysql if running on same network
      PORT: ${PORT} # 3306 is the default port of mysql
      DATABASE: ${DATABASE}
      # bunny CDN fields
      CLOUD_STORAGE_URL: ${CLOUD_STORAGE_URL}
      BUCKET_NAME: ${BUCKET_NAME}
      # email fields
      EMAIL: ${EMAIL}
      EMAIL_P: ${EMAIL_P}
      # proxy fields
      PROXY_USER: ${PROXY_USER}
      PROXY_PASS: ${PROXY_PASS}
      # typesense fields
      TS_HOST: ${TS_HOST} # localhost if running local, search.mydomain.com if running on remote
      TS_PORT: ${TS_PORT} # 8108 if running local, None if running on remote because Caddy will reverse proxy to typesense-sv:8108
      TS_PROTOCOL: ${TS_PROTOCOL}
      TS_API_KEY_RO: ${TS_API_KEY_RO} # created during typesense_setup
      # mongo db
      MONGO_CONNECTION_STRING: ${MONGO_CONNECTION_STRING}
    volumes:
      - ./logs:/code/logs
    networks:
      - flask_network

  typesense-sv: # for autocomplete search engine
    image: typesense/typesense:0.25.2
    restart: on-failure
    command: --data-dir /data --api-key=${TS_API_KEY_ADMIN} --listen-port 8108 --cors-domains ${CORS_DOMAINS}
    volumes:
      - typesense-data:/data
    ports:
      - 8108:8108
    networks:
      - flask_network

  typesense_setup: # populate collections and documents to typesense
    build: ./typesense_setup
    restart: on-failure
    environment:
      # db fields
      USER: ${USER}
      PASSWORD: ${MYSQL_ROOT_PASSWORD}
      HOST: db # Use this mysql if running on same network
      PORT: ${PORT} # 3306 is the default port of mysql
      DATABASE: ${DATABASE}
      # typesense fields
      TS_HOST: typesense-sv # don't change this. Internal communication between containers
      TS_API_KEY_ADMIN: ${TS_API_KEY_ADMIN} # value for running script to populate collections and documents
      TS_API_KEY_RO: ${TS_API_KEY_RO} # value for setting up readonly key to use in webapp
    networks:
      - flask_network
  
  db:
    image: mysql:8.0.35
    command: --innodb_ft_min_token_size=1
    restart: always
    ports:
      # - 3306:3306 # typical port
      - 3308:3306 # mapping 3306 of container to 3308 of host to avoid conflict with local mysql (which is running on 3306)
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DATABASE}
    volumes:
    # use this if you want to persist the data in Docker Volume
    # then add the volume name to the volumes section below
      - mysql_data:/var/lib/mysql
    # use this if you want to persist the data in host machine directory
      # - ./mysql_data_host:/var/lib/mysql
    networks:
      - flask_network

  mongodb:
    image: mongo:7.0.6
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    ports:
      - 27017:27017
    networks:
      - flask_network
  
  caddy: # for deploy on remote server to get SSL certificate
    image: caddy:2.6-alpine
    restart: always
    ports:
      - 80:80
      - 443:443
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - flask_network
  
  # proxy: # for deploy on local machine
  #   build: ./proxy
  #   restart: always
  #   ports:
  #     - 80:80
  #   networks:
  #     - flask_network

networks:
  flask_network:

volumes:
  mysql_data:
  caddy_data:
  caddy_config:
  typesense-data:
  mongodb_data: